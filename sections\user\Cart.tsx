"use client";

import { useEffect, useState } from "react";
import { CartItem } from "@/components/cards/CartItem";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { HeaderBase } from "@/components/layouts/core/header-base";
import { useBoolean } from "@/hooks/use-boolean";
import { useCartContext } from "@/context/hooks/use-cart-hook";
import { ShoppingBag, ChevronRight, Trash2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { set } from "date-fns";
import { LoginModal } from "@/components/login/LoginModal";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import AuthForm from "@/components/login/AuthFormModal";
import {
  getUserProfile,
  updateUserAddress,
  updateUserProfile,
} from "@/api/userProfile";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from "@/components/ui/select";
import { AddressModal } from "@/components/cart/AddressModal";
import { Address } from "@/types/address";
import { createAddress } from "@/api/address";
import { toast } from "sonner";
import { createCheckoutSession } from "@/api/stripe";
import { createUserOrder } from "@/api/order";
import { Label } from "@/components/ui/label";

interface CartItemType {
  _id: string;
  name: string;
  price: number;
  image: string;
  dishId: string;
  offering: string[];
  dineInTime: Date | null;
  pickupTime: Date | null;
  instructions: string;
  quantity: number;
}

interface CartHostType {
  hostId: {
    _id: string;
    title: string;
  };
  items: CartItemType[];
}

interface CartType {
  items: CartHostType[];
}

export default function OrderPage() {
  const [cart, setCart] = useState<CartType | null>(null);
  const [showloginModal, setShowloginModal] = useState(false);
  const { cartItems, updateCartItem, getCart, totalAmount, deleteCartItem } =
    useCartContext();
  const hamburgerMenu = useBoolean(false);
  const router = useRouter();
  const { authenticated, user, profile } = useAuthContext();
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<string | null>(null);
  const [userAddresses, setUserAddresses] = useState<Address[] | null>(null);

  // On mount, fetch cart items
  useEffect(() => {
    getCart();
  }, []);

  const handleRemoveItem = (hostId: string, dishId: string) => {
    setCart((prevCart) => {
      if (!prevCart) return null;
      return {
        ...prevCart,
        items: prevCart.items
          .map((host) => {
            if (host.hostId._id === hostId) {
              return {
                ...host,
                items: host.items.filter((item) => item.dishId !== dishId),
              };
            }
            return host;
          })
          .filter((host) => host.items.length > 0),
      };
    });
  };

  const handleUpdateQuantity = (
    hostId: string,
    dishId: string,
    newQuantity: number,
    price: number
  ) => {
    console.log(hostId, dishId, newQuantity, price);
    updateCartItem(dishId, newQuantity, hostId, price);
  };

  const calculateTotal = () => {
    return (
      cartItems?.reduce(
        (total, host) =>
          total +
          host.items.reduce(
            (hostTotal, item) => hostTotal + item.price * item.quantity,
            0
          ),
        0
      ) || 0
    );
  };
  useEffect(() => {
    setUserAddresses(profile?.address);
  }, [profile]);

  useEffect(() => {
    if (profile?.address?.length > 0 && !selectedAddress) {
      setSelectedAddress(profile.address[0]._id);
    }
  }, [profile, selectedAddress]);

  const handleAddressChange = (addressId: string) => {
    setSelectedAddress(addressId);
  };

  const hasDelivery = cartItems.some((host) =>
    host.items.some((item) => item.offering === "Delivery")
  ); // Create order from cart items
  const createOrder = async (paymentMethod: string) => {
    try {
      const hostGroups = cartItems.map((hostGroup) => {
        const hostId = hostGroup.hostId._id;
        const subtotal = hostGroup.items.reduce(
          (sum, item) => sum + item.price * item.quantity,
          0
        );

        return {
          hostId,
          items: hostGroup.items,
          subtotal,
          serviceFee: 0,
        };
      });

      const orderData = {
        hostGroups,
        shippingAddress: selectedAddress,
        tax: totalAmount * 0.1,
        paymentMethod,
      };

      const response = await createUserOrder(orderData);
      return response;
    } catch (error) {
      toast.error("Order Failed", {
        description:
          "There was a problem creating your order. Please try again.",
      });
      setIsProcessing(false);
      return null;
    }
  };
  const handleStripeCheckout = async () => {
    if (isProcessing) return;
    setIsProcessing(true);

    const hasDelivery = cartItems.some((host) =>
      host.items.some((item) => item.offering === "Delivery")
    );

    // Make sure address is selected for delivery orders
    if (hasDelivery && !selectedAddress) {
      toast.error("Address Required", {
        description: "Please select a delivery address.",
      });
      setIsProcessing(false);
      return;
    }

    try {
      // First create the order
      const order = await createOrder("card");
      if (!order) {
        setIsProcessing(false);
        return;
      }

      // Get the selected address object
      const selectedAddressObj = profile?.address?.find(
        (addr: Address) => addr._id === selectedAddress
      );

      // Create a checkout session
      const session = await createCheckoutSession(order._id);

      // Redirect to Stripe Checkout
      window.location.href = session.url;
    } catch (error) {
      toast.error("Checkout Failed", {
        description: "Failed to create checkout session. Please try again.",
      });
      setIsProcessing(false);
    }
  };

  const handleCheckout = async () => {
    handleStripeCheckout();
  };

  const addAddress = async (address: Address) => {
    const response = await updateUserAddress(address);

    if (response) {
      const updatedProfile = await getUserProfile();
      setUserAddresses(updatedProfile?.address);
      toast.success("Address Added", {
        description: "Your address has been added successfully.",
      });
    } else {
      toast.error("Address Failed", {
        description:
          "There was a problem adding your address. Please try again.",
      });
    }
  };
  console.log("User", profile);

  if (!cartItems || cartItems.length === 0) {
    return (
      <>
        <HeaderBase hamburgerMenu={hamburgerMenu} />
        <div className="max-w-4xl mx-auto px-4 py-16 text-center mt-28">
          <ShoppingBag className="w-24 h-24 mx-auto mb-8 text-gray-300" />
          <h1 className="text-3xl font-bold mb-4">Your cart is empty</h1>
          <p className="text-gray-600 mb-8">
            Looks like you haven't added any items to your cart yet.
          </p>
          <Button onClick={() => router.push("/")}>Start Shopping</Button>
        </div>
      </>
    );
  }

  const handleClearCart = () => {
    deleteCartItem();
    toast.success("Cart Cleared", {
      description: "Your cart has been cleared successfully.",
    });
  };
  return (
    <>
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      <div className="max-w-6xl mx-auto px-4 py-12 mt-28">
        <h1 className="text-4xl font-bold mb-12 text-center">Your Order</h1>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          <div className="lg:col-span-2">
            {cartItems.map((host) => (
              <div key={host.hostId._id} className="mb-12">
                <h2 className="text-2xl font-semibold mb-6 pb-2 border-b">
                  {host.hostId.title}
                </h2>
                {host.items.map((item) => (
                  <CartItem
                    key={item._id}
                    hostId={host.hostId._id}
                    item={item}
                    onRemove={handleRemoveItem}
                    onUpdateQuantity={handleUpdateQuantity}
                  />
                ))}
              </div>
            ))}
          </div>
          <div>
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle className="text-2xl">Order Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {cartItems.map((host) => (
                    <div key={host.hostId._id}>
                      <h3 className="font-semibold text-lg mb-2">
                        {host.hostId.title}
                      </h3>
                      {host.items.map((item) => (
                        <div
                          key={item._id}
                          className="flex justify-between text-sm"
                        >
                          <span>
                            {item.name} x{item.quantity}
                          </span>
                          <span className="font-medium">
                            ${(item.price * item.quantity).toFixed(2)}
                          </span>
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
                <div>
                  {hasDelivery && (
                    <div className="mt-6 w-full">
                      <Label htmlFor="address-select" className="block mb-2">
                        Delivery Address
                      </Label>
                      <Select
                        value={selectedAddress || ""}
                        onValueChange={handleAddressChange}
                      >
                        <SelectTrigger className="w-full" id="address-select">
                          <SelectValue placeholder="Select an address" />
                        </SelectTrigger>
                        <SelectContent className="max-w-[300px]">
                          <SelectGroup>
                            <SelectLabel>Your Addresses</SelectLabel>
                            {userAddresses &&
                              userAddresses.map((address: Address) => (
                                <SelectItem
                                  key={address._id}
                                  value={address._id || ""}
                                >
                                  {address.address}
                                </SelectItem>
                              ))}
                          </SelectGroup>
                          <AddressModal onAddressSubmit={addAddress} />
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex flex-col">
                <div className="flex justify-between w-full mb-6 text-lg font-semibold">
                  <span>Total</span>
                  <span>${totalAmount.toFixed(2)}</span>
                </div>
                <Button
                  variant="outline"
                  className="w-full mb-4 text-destructive border-destructive hover:bg-destructive/10"
                  onClick={handleClearCart}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Clear Cart
                </Button>

                {!authenticated ? (
                  <Dialog modal>
                    <DialogTrigger asChild>
                      <Button className="w-full text-lg py-6">
                        Login to Proceed
                        <ChevronRight className="ml-2 h-5 w-5" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px]">
                      <AuthForm />
                    </DialogContent>
                  </Dialog>
                ) : (
                  <Button
                    className="w-full text-lg py-6"
                    onClick={handleCheckout}
                  >
                    Proceed to Payment
                    <ChevronRight className="ml-2 h-5 w-5" />
                  </Button>
                )}
              </CardFooter>
            </Card>
          </div>
        </div>
        {}
      </div>
    </>
  );
}

# Message Read Status Implementation

## Overview

This document outlines the implementation of proper message read status handling in the ChatWindow component, utilizing the `chat:messages_read` WebSocket event and the enhanced message structure with `readBy` arrays.

## Message Structure

### Updated Message Interface

```typescript
interface ReadByEntry {
  user: string;        // User ID who read the message
  readAt: string;      // ISO timestamp when message was read
  _id: string;         // Unique identifier for the read entry
}

interface Message {
  _id: string;
  conversationId: string;
  content: string;
  sender: Profile;
  senderType: "user" | "host" | "system";
  createdAt: string;
  updatedAt: string;
  readBy: ReadByEntry[];  // Array of read status entries
  attachments?: Attachment[];
  isSystem?: boolean;
  __v?: number;
}
```

### API Response Example

```json
{
  "_id": "6842f0dbc4ff69f1b6577f8f",
  "conversation": "6842ea0d62fa43402f7bdd0f",
  "sender": {
    "_id": "68407f85f23b0620f53bf10e",
    "name": "New Jersey Cooks",
    "avatar": null
  },
  "content": "Hey",
  "readBy": [
    {
      "user": "673df23125dfca33707e6c87",
      "readAt": "2025-06-06T13:45:01.085Z",
      "_id": "6842f0ddc4ff69f1b6577f9c"
    }
  ],
  "createdAt": "2025-06-06T13:44:59.517Z",
  "updatedAt": "2025-06-06T13:45:01.085Z"
}
```

## WebSocket Events Handled

### 1. `chat:message_read`
- **Purpose**: Handle individual message read status updates
- **Data Structure**:
```typescript
interface MessageReadData {
  conversationId: string;
  userId: string;
  messageIds?: string[];
  readAt?: string;
  messages?: Message[];
}
```

### 2. `chat:messages_read` (New)
- **Purpose**: Handle bulk message read status updates
- **Use Case**: When a user reads multiple messages at once
- **Same data structure as `chat:message_read`**

## Implementation Details

### ChatWindow Component Changes

#### 1. Enhanced Event Listeners
```typescript
// Register both individual and bulk read event listeners
socket.on("chat:message_read", handleMessageRead);
socket.on("chat:messages_read", handleMessagesRead);
```

#### 2. Improved Read Status Handler
```typescript
const handleMessageRead = (data: MessageReadData) => {
  // Validate conversation
  if (data.conversationId !== conversation?._id) return;

  setMessages((prevMessages) => {
    return prevMessages.map((msg) => {
      // Check if message should be updated
      const shouldUpdate = !data.messageIds || data.messageIds.includes(msg._id);
      if (!shouldUpdate) return msg;

      // Prevent duplicate read entries
      const existingReadEntry = msg.readBy.find(entry => entry.user === data.userId);
      if (existingReadEntry) return msg;

      // Add new read entry
      const newReadEntry: ReadByEntry = {
        user: data.userId,
        readAt: data.readAt || new Date().toISOString(),
        _id: `read_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
      };

      return {
        ...msg,
        readBy: [...msg.readBy, newReadEntry],
        updatedAt: new Date().toISOString()
      };
    });
  });
};
```

#### 3. Optimistic Message Updates
```typescript
// When creating optimistic messages, include proper readBy structure
const optimisticMessage: Message = {
  // ... other fields
  readBy: [{
    user: currentUserId,
    readAt: currentTime,
    _id: `read_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`
  }],
  updatedAt: currentTime
};
```

### ChatMessage Component Changes

#### 1. Enhanced Read Status Detection
```typescript
const getReadStatus = () => {
  if (!message.readBy || message.readBy.length === 0) {
    return { isRead: false, readAt: null };
  }

  if (isOwnMessage) {
    // For own messages, check if other participant has read it
    const otherParticipantId = otherParticipant?._id || "";
    const readEntry = message.readBy.find(entry => entry.user === otherParticipantId);
    return {
      isRead: !!readEntry,
      readAt: readEntry?.readAt || null
    };
  } else {
    // For other's messages, check if current user has read it
    const currentUserId = currentUser?._id || currentUser?.hostId || "";
    const readEntry = message.readBy.find(entry => entry.user === currentUserId);
    return {
      isRead: !!readEntry,
      readAt: readEntry?.readAt || null
    };
  }
};
```

#### 2. Enhanced ReadStatusIcon with Timestamps
```typescript
const ReadStatusIcon = () => {
  if (!isOwnMessage) return null;

  const formatReadTime = (readTimestamp: string) => {
    const readDate = new Date(readTimestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - readDate.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    }
    return format(readDate, "MMM d, h:mm a");
  };

  if (isRead && readAt) {
    return (
      <span 
        className={`${styles.readStatus} ${styles.read}`} 
        title={`Read ${formatReadTime(readAt)}`}
      >
        ✓✓
      </span>
    );
  }
  
  return (
    <span className={styles.readStatus} title="Sent">
      ✓
    </span>
  );
};
```

## Features Implemented

### ✅ Dual Event Handling
- Supports both `chat:message_read` and `chat:messages_read` events
- Handles individual and bulk read status updates

### ✅ Duplicate Prevention
- Prevents duplicate read entries for the same user
- Maintains data consistency across state updates

### ✅ Enhanced Visual Indicators
- Shows detailed read timestamps in tooltips
- Displays relative time (e.g., "2 minutes ago", "Just now")
- Fallback to formatted date for older messages

### ✅ Bidirectional Read Status
- Tracks when current user reads others' messages
- Tracks when others read current user's messages
- Proper participant identification for hosts and users

### ✅ Optimistic Updates
- Immediate read status for sent messages
- Proper structure for temporary messages

### ✅ Robust Error Handling
- Conversation ID validation
- Graceful handling of missing data
- Fallback values for edge cases

## Testing Scenarios

### 1. Individual Message Read
- Send message from User A to User B
- Verify User B sees "✓" (sent) initially
- When User A reads message, verify User B sees "✓✓" (read)

### 2. Bulk Message Read
- Send multiple messages from User A to User B
- User A reads all messages at once
- Verify all messages show read status for User B

### 3. Read Timestamp Display
- Hover over read status icons
- Verify tooltips show accurate relative timestamps
- Test with messages of different ages

### 4. Cross-Conversation Isolation
- Have multiple conversations open
- Verify read status updates only affect correct conversation

### 5. Connection Recovery
- Disconnect and reconnect WebSocket
- Verify read status updates continue to work properly

## Maintenance Notes

- Monitor console logs for read status processing
- Watch for duplicate read entries (should not occur)
- Ensure proper cleanup of event listeners
- Validate conversation ID matching in all handlers

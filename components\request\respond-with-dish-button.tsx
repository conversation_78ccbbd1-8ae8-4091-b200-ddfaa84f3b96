"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { ChefHat, Plus } from "lucide-react";
import { toast } from "sonner";
import AddDish from "@/components/addDish";
import { useBoolean } from "@/hooks/use-boolean";
import {
  createDishHost,
  getAllCuisines,
  getAllIngredients,
  getHostProfile,
  getPremadeDishes,
} from "@/api/host";
import { DiningLocations, Dish } from "@/types/host";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import DishCard from "@/components/cards/DishCard";

interface RespondWithDishButtonProps {
  requestId: string;
  ingredients?: string[];
  minServings?: number;
  maxServings?: number;
  offering?: string;
}

type CuisineOption = {
  value: string;
  label: string;
  code: string;
  subcuisines: string[];
};
type IngredientOption = {
  value: string;
  label: string;
};

export default function RespondWithDishButton({
  requestId,
  minServings = 1,
  maxServings = 4,
  offering = "Delivery",
}: RespondWithDishButtonProps) {
  const { host } = useAuthContext();
  console.log("host", host);
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [isSelectDishOpen, setIsSelectDishOpen] = useState(false);
  const addDishModal = useBoolean();
  const [premadeDishes, setPremadeDishes] = useState<Dish[]>([]);
  const [ingredients, setIngredients] = useState<IngredientOption[]>([]);
  const [cuisines, setCuisines] = useState<CuisineOption[]>([]);
  const [diningLocations, setDiningLocations] = useState<DiningLocations[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleCreateNewDish = () => {
    setIsOpen(false);
    addDishModal.onTrue();
  };

  const handleSelectExistingDish = () => {
    setIsOpen(false);
    setIsSelectDishOpen(true);
  };

  const handleDishSelect = (dish: Dish) => {
    setIsSelectDishOpen(false);
    router.push(`/requests/${requestId}/respond?dishId=${dish._id}`);
  };
  useEffect(() => {
    const fetchIngredients = async () => {
      try {
        const response = await getAllIngredients();
        setIngredients(
          response.data.map((ingredient: { _id: string; name: string }) => ({
            value: ingredient._id, // Add the value property
            label: ingredient.name,
          }))
        );
      } catch (error) {
        toast.error("Failed to fetch ingredients");
      }
    };

    const fetchCuisines = async () => {
      try {
        const response = await getAllCuisines();
        setCuisines(
          response.data.map(
            (cuisine: {
              _id: string;
              name: string;
              subcuisines: string[];
            }) => ({
              value: cuisine._id,
              label: cuisine.name,
              subcuisines: cuisine.subcuisines,
            })
          )
        );
      } catch (error) {
        toast.error("Failed to fetch cuisines");
      }
    };

    const fetchDishes = async () => {
      try {
        const response = await getPremadeDishes();
        console.log("🚀 ~ fetchDishes ~ response", response.data);
        setPremadeDishes(response.data);
      } catch (error) {
        toast.error("Failed to fetch dishes");
      }
    };
    const fetchDiningLocations = async () => {
      try {
        const response = await getHostProfile();
        console.log("🚀 ~ fetchDiningLocations ~ response", response.data);
        setDiningLocations(response.data.diningLocations);
      } catch (error) {
        toast.error("Failed to fetch dining locations");
      }
    };

    fetchIngredients();
    fetchCuisines();
    fetchDishes();
    fetchDiningLocations();
  }, []);
  const handleAddDish = async (newDish: any) => {
    setIsSubmitting(true);
    try {
      // Set the dish as not premade
      newDish.premade = false;

      // Add request-specific data
      newDish.request = true;

      // Create the dish
      const response = await createDishHost(newDish);

      // Get the created dish ID
      const dishId = response.data.dishes[response.data.dishes.length - 1]._id;

      toast.success("Dish created successfully");

      // Redirect to respond page with the dish ID
      router.push(`/requests/${requestId}/respond?dishId=${dishId}`);
    } catch (error) {
      console.error("Error creating dish:", error);
      toast.error("Failed to create dish");
    } finally {
      setIsSubmitting(false);
      addDishModal.onFalse();
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="flex items-center">
            <ChefHat className="h-4 w-4 mr-1" /> Respond with Dish
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Respond with a Dish</DialogTitle>
            <DialogDescription>
              Create a new dish or select an existing one to respond to this
              request.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
            <Button
              variant="outline"
              className="h-24 flex flex-col items-center justify-center gap-2"
              onClick={handleCreateNewDish}
            >
              <Plus className="h-6 w-6" />
              <div>
                <div className="font-medium">Create New Dish</div>
                <div className="text-xs text-muted-foreground">
                  Design a dish specifically for this request
                </div>
              </div>
            </Button>
          </div>
          <DialogFooter>
            <Button variant="ghost" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isSelectDishOpen} onOpenChange={setIsSelectDishOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Select Existing Dish</DialogTitle>
            <DialogDescription>
              Choose one of your existing dishes to respond to this request
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 py-4 max-h-[60vh] overflow-y-auto">
            {host?.dishes?.map((dish: Dish) => (
              <div
                key={dish._id}
                className="cursor-pointer transition-transform hover:scale-105"
                onClick={() => handleDishSelect(dish)}
              >
                <DishCard dish={dish} hideActions />
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button variant="ghost" onClick={() => setIsSelectDishOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {addDishModal.value && (
        <AddDish
          onClose={addDishModal.onFalse}
          onAddDish={handleAddDish}
          ingredients={ingredients}
          cuisines={cuisines}
          premadeDishes={premadeDishes}
          diningLocations={diningLocations}
        />
      )}
    </>
  );
}

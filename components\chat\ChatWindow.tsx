"use client";

import React, { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { formatDistanceToNow } from "date-fns";
import SocketContext from "@/context/SocketContext";
import ChatMessage from "./ChatMessage";
import ChatInput from "./ChatInput";
import styles from "@/styles/Chat.module.css";
import { getMessages, sendMessage, uploadChatFiles } from "@/api/chat";

interface Profile {
  _id: string;
  name?: string;
  avatar?: string;
  title?: string;
}

interface ReadByEntry {
  user: string;
  readAt: string;
  _id: string;
}

interface Message {
  _id: string;
  conversationId: string;
  content: string;
  sender: Profile;
  senderType: "user" | "host" | "system";
  createdAt: string;
  updatedAt: string;
  readBy: ReadByEntry[];
  attachments?: {
    url: string;
    type: string;
    name: string;
  }[];
  isSystem?: boolean;
  __v?: number;
}

interface MessageRecievedData {
  conversationId: string;
  message: Message;
  success: boolean;
}

interface MessageSentData {
  conversationId: string;
  message?: Message; // Optional - server might send full message or just messageId
  messageId?: string; // Alternative format from server
  success: boolean;
}

interface MessageReadData {
  conversationId: string;
  userId: string;
  messageIds?: string[];
  readAt?: string;
  messages?: Message[]; // For bulk read updates
}

interface TypingData {
  conversationId: string;
  userId: string;
  userName: string;
  isTyping: boolean;
}

interface ChatWindowProps {
  conversation: any;
  user: any;
  host: any;
}

const ChatWindow: React.FC<ChatWindowProps> = ({
  conversation,
  user,
  host,
}) => {
  const componentId = React.useRef(
    `ChatWindow_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  );
  const { socket, isConnected } = React.useContext(SocketContext);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageIds, setMessageIds] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const [typingUser, setTypingUser] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tempMessageRef = useRef<Message | null>(null);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const processedMessageIdsRef = useRef(new Set<string>()); // Ref to track processed message IDs
  const messageProcessingTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Debounce message processing

  console.log("🔄 [COMPONENT DEBUG] ChatWindow mounted/re-rendered:", {
    componentId: componentId.current,
    conversationId: conversation?._id,
    socketId: socket?.id,
    isConnected,
    timestamp: new Date().toISOString(),
  });
  // Determine if the current user is the host or the customer
  const isHost = host?.user === conversation?.host?._id;

  // Get the other participant's info
  const otherParticipant = isHost
    ? conversation?.user
    : conversation?.hostProfile || conversation?.host;

  // Fetch messages when conversation changes
  useEffect(() => {
    const fetchMessages = async () => {
      if (!conversation?._id) return;

      try {
        setLoading(true);
        const data = await getMessages(conversation._id);
        const fetchedMessages = data.data || [];
        setMessages(fetchedMessages);

        // Update message IDs set for deduplication
        setMessageIds(new Set(fetchedMessages.map((msg: Message) => msg._id)));
      } catch (err) {
        console.error("Error fetching messages:", err);
        setError("Failed to load messages. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    fetchMessages();

    // Join the conversation room when the conversation changes
    if (socket && isConnected && conversation?._id) {
      socket.emit("chat:join_conversation", {
        conversationId: conversation._id,
      });

      // Mark messages as read
      socket.emit("chat:read_messages", { conversationId: conversation._id });
    }

    // Leave the conversation room when component unmounts or conversation changes
    return () => {
      if (socket && isConnected && conversation?._id) {
        socket.emit("chat:leave_conversation", {
          conversationId: conversation._id,
        });
      }
    };
  }, [conversation?._id, socket, isConnected]);

  // Listen for new messages
  useEffect(() => {
    console.group("🔌 [CHAT] Setting up socket listeners");
    console.log("Socket available:", !!socket);
    console.log("Socket connected:", isConnected);
    console.log("Socket ID:", socket?.id);
    console.log("Conversation ID:", conversation?._id);
    console.groupEnd();

    if (!socket || !isConnected || !conversation?._id) {
      console.warn(
        "⚠️ [CHAT] Socket not available, not connected, or no conversation ID - skipping listener setup"
      );
      return;
    }

    // Clear the processed message IDs ref when listeners are reset (e.g., new conversation)
    processedMessageIdsRef.current.clear();
    console.log(
      "[CHAT LISTENER DEBUG] Cleared processedMessageIdsRef for new listener setup."
    );

    const handlerInstanceId = `handler_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 7)}`;
    console.log(
      `[CHAT LISTENER DEBUG] Defining handlers with instance ID: ${handlerInstanceId} for conv: ${conversation?._id}, socket: ${socket?.id}`
    );

    // CRITICAL FIX: Remove any existing listeners for these events before adding new ones
    // This prevents duplicate event listeners from accumulating
    socket.off("chat:message_received");
    socket.off("chat:user_typing");
    socket.off("chat:message_read");
    socket.off("chat:messages_read");
    socket.off("chat:message_sent");

    console.log(
      "🧹 [CHAT] Removed any existing listeners before registering new ones"
    );

    const handleNewMessage = (data: MessageRecievedData) => {
      console.log(
        `🔵 [DUPLICATE DEBUG] Message received event triggered by handler instance: ${handlerInstanceId}`,
        {
          messageId: data.message._id,
          conversationId: data.conversationId,
          expectedConversationId: conversation?._id,
          matches: data.conversationId === conversation?._id,
          socketId: socket?.id,
          timestamp: new Date().toISOString(),
        }
      );

      // CRITICAL FIX: Early return if not for this conversation
      if (data.conversationId !== conversation?._id) {
        console.log(
          `[CHAT] Message ${data.message._id} for other conversation (${data.conversationId}), skipping.`
        );
        return;
      }

      const messageId = data.message._id;

      // CRITICAL FIX: Enhanced duplicate detection using ref
      if (processedMessageIdsRef.current.has(messageId)) {
        console.warn(
          `⚠️ [REF DUPLICATE] Message ${messageId} already processed by ref for this listener setup. Skipping.`
        );
        return;
      }

      // Mark as processed immediately to prevent race conditions
      processedMessageIdsRef.current.add(messageId);
      console.log(
        `[REF PROCESS] Marked message ${messageId} as processed by ref.`
      );

      console.log("🔵 New message received (after ref check):", {
        messageId: data.message._id,
        content: data.message.content,
        senderId: data.message.sender._id,
        senderName: data.message.sender.name,
      });

      // CRITICAL FIX: Use functional state update with additional duplicate check
      setMessages((prevMessages) => {
        // Double-check to prevent duplicates in state
        const messageExists = prevMessages.some((msg) => msg._id === messageId);
        if (messageExists) {
          console.warn(
            `⚠️ [STATE DUPLICATE] Message ${messageId} already exists in messages array. Skipping add.`
          );
          return prevMessages;
        }

        console.log("✅ [SETMESSAGES] Adding new message to messages list:", {
          messageId: messageId,
          previousCount: prevMessages.length,
          newCount: prevMessages.length + 1,
        });
        return [...prevMessages, data.message];
      });

      // Update messageIds set for consistency
      setMessageIds((prevIds) => {
        if (prevIds.has(messageId)) {
          console.log(
            `[MESSAGEIDS] ID ${messageId} already in set, no change needed.`
          );
          return prevIds;
        }
        console.log(
          `✅ [MESSAGEIDS] Adding ID ${messageId} to messageIds set.`
        );
        const newIds = new Set(prevIds);
        newIds.add(messageId);
        return newIds;
      });

      // Mark message as read if it's not from the current user
      const currentUserId = user?._id || host?.user;
      const isOwnMessage = data.message.sender._id === currentUserId;
      if (!isOwnMessage && socket && conversation) {
        socket.emit("chat:read_messages", {
          conversationId: conversation._id,
        });
      }
    };

    const handleTyping = (data: TypingData) => {
      if (
        data.conversationId === conversation?._id &&
        data.userId !== user?._id &&
        data.userId !== host?.user
      ) {
        setIsTyping(data.isTyping);
        setTypingUser(data.userName || "Someone");
      }
    };

    const handleMessageRead = (data: MessageReadData) => {
      if (data.conversationId !== conversation?._id) {
        console.log(
          `[CHAT] Message read event for other conversation (${data.conversationId}), skipping.`
        );
        return;
      }

      console.log("📖 Message read event received:", data);

      // Handle both individual message read and bulk message read updates
      setMessages((prevMessages) => {
        return prevMessages.map((msg) => {
          // Check if this message should be updated
          const shouldUpdate =
            !data.messageIds || data.messageIds.includes(msg._id);

          if (!shouldUpdate) {
            return msg;
          }

          // Check if user already marked as read
          const existingReadEntry = msg.readBy.find(
            (entry) => entry.user === data.userId
          );
          if (existingReadEntry) {
            console.log(
              `Message ${msg._id} already marked as read by user ${data.userId}`
            );
            return msg;
          }

          // Add new read entry
          const newReadEntry: ReadByEntry = {
            user: data.userId,
            readAt: data.readAt || new Date().toISOString(),
            _id: `read_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 7)}`,
          };

          console.log(
            `✅ [READ STATUS] Marking message ${msg._id} as read by user ${data.userId}`
          );

          return {
            ...msg,
            readBy: [...msg.readBy, newReadEntry],
            updatedAt: new Date().toISOString(),
          };
        });
      });
    };

    // Handle bulk messages read event (chat:messages_read)
    const handleMessagesRead = (data: MessageReadData) => {
      if (data.conversationId !== conversation?._id) {
        console.log(
          `[CHAT] Messages read event for other conversation (${data.conversationId}), skipping.`
        );
        return;
      }

      console.log("📖 Bulk messages read event received:", data);

      // Use the same logic as handleMessageRead but with explicit bulk handling
      setMessages((prevMessages) => {
        return prevMessages.map((msg) => {
          // For bulk read, update all messages if no specific messageIds provided
          // or only update messages in the provided messageIds array
          const shouldUpdate =
            !data.messageIds || data.messageIds.includes(msg._id);

          if (!shouldUpdate) {
            return msg;
          }

          // Check if user already marked as read
          const existingReadEntry = msg.readBy.find(
            (entry) => entry.user === data.userId
          );
          if (existingReadEntry) {
            return msg;
          }

          // Add new read entry
          const newReadEntry: ReadByEntry = {
            user: data.userId,
            readAt: data.readAt || new Date().toISOString(),
            _id: `read_${Date.now()}_${Math.random()
              .toString(36)
              .substring(2, 7)}`,
          };

          console.log(
            `✅ [BULK READ] Marking message ${msg._id} as read by user ${data.userId}`
          );

          return {
            ...msg,
            readBy: [...msg.readBy, newReadEntry],
            updatedAt: new Date().toISOString(),
          };
        });
      });
    };

    // Handle message sent confirmation - works with both response formats
    const handleMessageSent = (data: MessageSentData) => {
      console.group("📧 [CHAT] Message sent confirmation received");
      console.log("Full data:", data);
      console.log(
        "Response format:",
        data.message ? "Full message" : "MessageId only"
      );
      console.log(
        "Conversation ID match:",
        data.conversationId === conversation?._id
      );
      console.groupEnd();

      if (data.conversationId === conversation?._id && data.success) {
        if (data.message) {
          // Server sent full message object - add it if not already present
          setMessageIds((prevIds) => {
            if (prevIds.has(data.message!._id)) {
              console.log(
                "⚠️ Duplicate sent message detected, skipping:",
                data.message!._id
              );
              return prevIds;
            }

            setMessages((prev) => [...prev, data.message!]);
            console.log("✅ [CHAT] Added confirmed message to display:", {
              messageId: data.message!._id,
              content: data.message!.content?.substring(0, 30),
            });

            return new Set([...Array.from(prevIds), data.message!._id]);
          });
        } else if (data.messageId) {
          // Server sent only messageId - update optimistic message with real ID
          setMessages((prev) =>
            prev.map((msg) => {
              if (
                msg._id.startsWith("temp_") &&
                msg.content === tempMessageRef.current?.content
              ) {
                const updatedMsg = { ...msg, _id: data.messageId! };
                console.log("✅ [CHAT] Updated temp message with real ID:", {
                  tempId: msg._id,
                  realId: data.messageId,
                });
                return updatedMsg;
              }
              return msg;
            })
          );

          // Update message IDs set
          setMessageIds((prevIds) => {
            const newIds = new Set(prevIds);
            // Remove temp ID and add real ID
            Array.from(prevIds).forEach((id) => {
              if (id.startsWith("temp_")) {
                newIds.delete(id);
              }
            });
            newIds.add(data.messageId!);
            return newIds;
          });
        }

        // Clear temp message reference
        tempMessageRef.current = null;
      } else if (!data.success) {
        // Remove failed optimistic message
        console.error(
          "❌ [CHAT] Message send failed, removing optimistic message"
        );
        setMessages((prev) =>
          prev.filter((msg) => !msg._id.startsWith("temp_"))
        );
        setMessageIds((prevIds) => {
          const newIds = new Set(prevIds);
          Array.from(prevIds).forEach((id) => {
            if (id.startsWith("temp_")) {
              newIds.delete(id);
            }
          });
          return newIds;
        });
        tempMessageRef.current = null;
      }
    };

    console.log(
      `[CHAT LISTENER DEBUG] Registering 'chat:message_received' with handler instance: ${handlerInstanceId} (socketId: ${socket.id}, convId: ${conversation?._id})`
    );
    console.log("🎧 [CHAT] Registering socket event listeners");

    socket.on("chat:message_received", handleNewMessage);
    socket.on("chat:user_typing", handleTyping);
    socket.on("chat:message_read", handleMessageRead);
    socket.on("chat:messages_read", handleMessagesRead);
    socket.on("chat:message_sent", handleMessageSent);

    console.log(
      "✅ [CHAT] Socket listeners registered successfully for instance:",
      handlerInstanceId
    );

    return () => {
      console.log(
        `[CHAT LISTENER DEBUG] Cleanup initiated for handler instance: ${handlerInstanceId} (socketId: ${socket?.id}, convId: ${conversation?._id})`
      );
      console.log(
        "🧹 [CHAT] Cleaning up socket event listeners for this effect instance."
      );

      // CRITICAL FIX: Enhanced cleanup - remove all listeners for these events
      // This ensures no duplicate listeners remain
      if (socket) {
        socket.off("chat:message_received", handleNewMessage);
        socket.off("chat:user_typing", handleTyping);
        socket.off("chat:message_read", handleMessageRead);
        socket.off("chat:messages_read", handleMessagesRead);
        socket.off("chat:message_sent", handleMessageSent);

        // Additional cleanup: remove all listeners for these events to be extra safe
        socket.removeAllListeners("chat:message_received");
        socket.removeAllListeners("chat:user_typing");
        socket.removeAllListeners("chat:message_read");
        socket.removeAllListeners("chat:messages_read");
        socket.removeAllListeners("chat:message_sent");
      }

      console.log(
        `✅ [CHAT] Socket listeners for handler instance ${handlerInstanceId} cleaned up`
      );
    };
  }, [socket?.id, isConnected, conversation?._id]); // CRITICAL FIX: Use socket.id instead of socket object to prevent unnecessary re-runs

  // Enhanced scroll to bottom when messages change or typing indicator appears
  useEffect(() => {
    const scrollToBottomSmoothly = () => {
      if (messagesEndRef.current && shouldAutoScroll) {
        messagesEndRef.current.scrollIntoView({
          behavior: "smooth",
          block: "end",
          inline: "nearest",
        });
      }
    };

    // Small delay to ensure DOM has updated
    const timeoutId = setTimeout(scrollToBottomSmoothly, 100);

    return () => clearTimeout(timeoutId);
  }, [messages, isTyping, shouldAutoScroll]);

  // Monitor scroll position to determine if user has scrolled up
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
      setShouldAutoScroll(isNearBottom);
    };

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, []);

  // Immediate scroll for new messages from current user
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      const currentUserId = user?._id || host?.user;

      // Always scroll for own messages or when auto-scroll is enabled
      if (lastMessage.sender._id === currentUserId || shouldAutoScroll) {
        setTimeout(() => {
          if (messagesEndRef.current) {
            messagesEndRef.current.scrollIntoView({
              behavior: "smooth",
              block: "end",
            });
          }
        }, 50);
      }
    }
  }, [messages.length, user?._id, host?.user, shouldAutoScroll]);

  // Handle sending a message with optimistic updates
  const handleSendMessage = React.useCallback(
    async (content: string, attachments?: File[]) => {
      if (!content.trim() && (!attachments || attachments.length === 0)) return;

      const currentUserId = user?._id || host?.user;
      const tempId = `temp_${Date.now()}_${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      try {
        // If there are attachments, upload them first
        let uploadedAttachments = [];

        if (attachments && attachments.length > 0) {
          const uploadData = await uploadChatFiles(attachments);
          uploadedAttachments = uploadData.files;
        }

        // Create optimistic message
        const currentTime = new Date().toISOString();
        const optimisticMessage: Message = {
          _id: tempId,
          conversationId: conversation._id,
          content,
          sender: {
            _id: currentUserId,
            name: user?.name || host?.name || "You",
            avatar: user?.avatar || host?.avatar,
          },
          senderType: user ? "user" : "host",
          createdAt: currentTime,
          updatedAt: currentTime,
          readBy: [
            {
              user: currentUserId,
              readAt: currentTime,
              _id: `read_${Date.now()}_${Math.random()
                .toString(36)
                .substring(2, 7)}`,
            },
          ],
          attachments: uploadedAttachments,
        };

        // Store reference for confirmation handling
        tempMessageRef.current = optimisticMessage;

        // Add optimistic message immediately (with duplicate protection)
        setMessages((prev) => {
          // Check if temp message already exists
          const tempExists = prev.some((msg) => msg._id === tempId);
          if (tempExists) {
            console.log(
              "⚠️ [CHAT] Temp message already exists, skipping optimistic add:",
              tempId
            );
            return prev;
          }

          console.log(
            "📝 [CHAT] Adding optimistic message to messages array:",
            {
              currentCount: prev.length,
              newMessageId: tempId,
              content: content.substring(0, 30),
            }
          );
          return [...prev, optimisticMessage];
        });

        setMessageIds((prevIds) => {
          if (prevIds.has(tempId)) {
            console.log(
              "⚠️ [CHAT] Temp message ID already in set, skipping:",
              tempId
            );
            return prevIds;
          }
          return new Set([...Array.from(prevIds), tempId]);
        });

        console.log("✅ [CHAT] Added optimistic message:", {
          tempId,
          content: content.substring(0, 30),
          totalMessages: messages.length + 1,
        });

        // Send message via socket
        if (socket && isConnected) {
          console.log("📤 Sending message via socket:", {
            conversationId: conversation._id,
            content,
            attachments: uploadedAttachments,
          });

          socket.emit("chat:send_message", {
            conversationId: conversation._id,
            content,
            attachments: uploadedAttachments,
          });
        } else {
          // Fallback to REST API if socket is not connected
          console.log("📤 Sending message via REST API (socket not connected)");
          const response = await sendMessage(
            conversation._id,
            content,
            uploadedAttachments
          );

          // Update optimistic message with real data
          if (response?.data) {
            setMessages((prev) =>
              prev.map((msg) =>
                msg._id === tempId
                  ? { ...response.data, _id: response.data._id }
                  : msg
              )
            );
            setMessageIds((prevIds) => {
              const newIds = new Set(prevIds);
              newIds.delete(tempId);
              newIds.add(response.data._id);
              return newIds;
            });
          }

          tempMessageRef.current = null;
        }
      } catch (err) {
        console.error("❌ Error sending message:", err);

        // Remove failed optimistic message
        setMessages((prev) => prev.filter((msg) => msg._id !== tempId));
        setMessageIds((prevIds) => {
          const newIds = new Set(prevIds);
          newIds.delete(tempId);
          return newIds;
        });

        tempMessageRef.current = null;
        alert("Failed to send message. Please try again.");
      }
    },
    [conversation._id, user, host, socket, isConnected]
  );

  // Handle typing indicator
  const handleTyping = React.useCallback(
    (isTyping: boolean) => {
      if (socket && isConnected) {
        socket.emit("chat:typing", {
          conversationId: conversation._id,
          isTyping,
        });
      }
    },
    [socket, isConnected, conversation._id]
  );

  // Group messages by date
  const groupMessagesByDate = () => {
    const groups: { [key: string]: Message[] } = {};

    messages.forEach((message) => {
      const date = new Date(message.createdAt).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(message);
    });

    return groups;
  };

  // Format date for display
  const formatMessageDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
        year: "numeric",
      });
    }
  };

  // View order details
  const viewOrderDetails = () => {
    if (conversation?.order?._id) {
      window.open(`/orders/${conversation.order._id}`, "_blank");
    }
  };

  if (loading) {
    return <div className={styles.loading}>Loading messages...</div>;
  }

  if (error) {
    return <div className={styles.error}>{error}</div>;
  }

  const messageGroups = groupMessagesByDate();

  return (
    <div className={styles.chatWindowContainer}>
      <div className={styles.chatHeader}>
        <div className={styles.participantInfo}>
          <div className={styles.avatar}>
            {otherParticipant?.avatar ? (
              <Image
                src={otherParticipant.avatar}
                alt={
                  otherParticipant.name ||
                  otherParticipant.title ||
                  "Participant"
                }
                width={40}
                height={40}
                className={styles.avatarImage}
              />
            ) : (
              <div className={styles.defaultAvatar}>
                {(
                  otherParticipant?.name ||
                  otherParticipant?.title ||
                  "U"
                ).charAt(0)}
              </div>
            )}
            {conversation?.status === "active" && (
              <span className={styles.statusIndicator}></span>
            )}
          </div>
          <div className={styles.participantDetails}>
            <h3>
              {otherParticipant?.name || otherParticipant?.title || "User"}
            </h3>
            <span className={styles.status}>
              {conversation?.status === "active" ? "Online" : "Offline"}
            </span>
          </div>
        </div>
        <div className={styles.chatActions}>
          <button className={styles.orderButton} onClick={viewOrderDetails}>
            See order details
          </button>
          <button className={styles.moreButton}>
            <span className={styles.moreIcon}>⋮</span>
          </button>
        </div>
      </div>

      <div className={styles.messagesContainer} ref={messagesContainerRef}>
        {Object.keys(messageGroups).length === 0 ? (
          <div className={styles.noMessages}>
            No messages yet. Start the conversation!
          </div>
        ) : (
          Object.entries(messageGroups).map(([date, msgs]) => (
            <div key={date} className={styles.messageGroup}>
              <div className={styles.dateHeader}>
                <span>{formatMessageDate(date)}</span>
              </div>
              {msgs.map((message) => (
                <ChatMessage
                  key={message._id}
                  message={message}
                  isOwnMessage={
                    message.sender._id === user?._id ||
                    message.sender._id === host?.user
                  }
                  otherParticipant={otherParticipant}
                  currentUser={user}
                />
              ))}
            </div>
          ))
        )}

        {isTyping && (
          <div className={styles.typingIndicator}>
            {typingUser} is typing...
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      <ChatInput
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
        disabled={conversation?.status === "archived"}
      />
    </div>
  );
};

export default ChatWindow;

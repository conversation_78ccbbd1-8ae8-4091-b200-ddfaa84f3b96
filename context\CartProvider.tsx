"use client";
import {
  use<PERSON>em<PERSON>,
  useEffect,
  use<PERSON><PERSON>back,
  create<PERSON>ontex<PERSON>,
  useContext,
  ReactNode,
  Context,
} from "react";
import { useSetState } from "@/hooks/use-set-state";
import {
  addItemToCart,
  deleteCart,
  getUserCart,
  updateItemToCart,
} from "@/api/cart";

interface CartItemType {
  _id: string;
  name: string;
  price: number;
  image: string;
  dishId: string;
  offering: string;
  dineInTime: Date | null;
  pickupTime: Date | null;
  instructions: string;
  quantity: number;
}

interface CartHostType {
  hostId: {
    _id: string;
    title: string;
  };
  items: CartItemType[];
}
interface CartItem {
  id: string;
  product: { price: number; name: string };
  quantity: number;
  cartDetails?: { cartId: string; hostId: string }; // Add otherDetails here
}

interface CartState {
  items: CartHostType[];
  totalAmount: number;
  loading: boolean;
  cartDetails?: { cartId: string; hostId: string }; // Add otherDetails here
}

interface CartProviderProps {
  children: ReactNode;
}

interface CartContextProps {
  cartItems: CartHostType[];
  totalAmount: number;
  loading: boolean;
  cartDetails?: { cartId: string; hostId: string }; // Add otherDetails here
  getCart: () => Promise<void>;
  createCartItem: (
    id: string,
    name: string,
    price: number,
    quantity: number,
    image: string,
    offering: string,
    hostId: string,
    dineInTime: Date | null,
    pickupTime: Date | null
  ) => Promise<void>;
  updateCartItem: (
    id: string,
    quantity: number,
    hostId: string,
    price: number
  ) => Promise<void>;
  deleteCartItem: () => Promise<void>;
}

export const CartContext: Context<CartContextProps | undefined> = createContext<
  CartContextProps | undefined
>(undefined);

export function CartProvider({ children }: CartProviderProps) {
  const { state, setState } = useSetState<CartState>({
    items: [],
    totalAmount: 0,
    cartDetails: { cartId: "", hostId: "" },
    loading: false,
  });

  const getCart = useCallback(async () => {
    setState({ loading: true });
    try {
      const response = await getUserCart();
      console.log(response);
      setState({
        items: response?.items,
        cartDetails: { cartId: response?._id, hostId: response?.host },
        totalAmount: response.total,
        loading: false,
      });
    } catch (error) {
      console.error("Error fetching cart:", error);
      setState({ loading: false });
    }
  }, [setState]);

  const createCartItem = useCallback(
    async (
      dishId: string,
      name: string,
      price: number,
      quantity: number,
      image: string,
      offering: string,
      hostId: string,
      dineInTime: Date | null,
      pickupTime: Date | null
    ) => {
      setState({ loading: true });
      try {
        const resp = await addItemToCart(
          dishId,
          name,
          price,
          image,
          quantity,
          offering,
          hostId,
          dineInTime,
          pickupTime
        );
        console.log(resp);
        setState({
          items: resp?.items,
          totalAmount: resp?.total,
          cartDetails: { cartId: resp?._id, hostId: resp?.host },
          loading: false,
        });
      } catch (error) {
        console.error("Error creating cart item:", error);
        setState({ loading: false });
      }
    },
    [setState]
  );

  const updateCartItem = useCallback(
    async (dishId: string, quantity: number, hostId: string, price: number) => {
      setState({ loading: true });
      try {
        const resp = await updateItemToCart(dishId, quantity, hostId, price);
        setState({
          items: resp?.items,
          totalAmount: resp?.total,
          cartDetails: { cartId: resp?._id, hostId: resp?.hostId },
          loading: false,
        });
      } catch (error) {
        console.error("Error updating cart item:", error);
        setState({ loading: false });
      }
    },
    [setState]
  );

  const deleteCartItem = useCallback(async () => {
    setState({ loading: true });
    try {
      await deleteCart();
      setState({ items: [], loading: false });
    } catch (error) {
      console.error("Error deleting cart item:", error);
      setState({ loading: false });
    }
  }, [setState]);

  useEffect(() => {
    getCart(); // Fetch cart on component mount
  }, [getCart]);

  const value = useMemo(
    () => ({
      cartItems: state.items,
      totalAmount: state.totalAmount,
      loading: state.loading,
      cartDetails: state.cartDetails,
      getCart,
      createCartItem,
      updateCartItem,
      deleteCartItem,
    }),
    [state, getCart, createCartItem, updateCartItem, deleteCartItem]
  );

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

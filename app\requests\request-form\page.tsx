"use client";

import DishRequestForm from "@/components/request/requestForm";
import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { HeaderBase } from "@/components/layouts/core/header-base";
import { useBoolean } from "@/hooks/use-boolean";
import { useRouter } from "next/navigation";

export default function RequestFormPage() {
  // State to detect if the page is being rendered on a mobile device
  const [isMobile, setIsMobile] = useState(false);
  const router = useRouter();
  const { toast } = require("sonner");

  // Effect to check screen size on mount and window resize
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Check on initial render
    checkScreenSize();

    // Add event listener for window resize
    window.addEventListener("resize", checkScreenSize);

    // Clean up the event listener on component unmount
    return () => window.removeEventListener("resize", checkScreenSize);
  }, []);

  // State to manage the mobile menu
  const hamburgerMenu = useBoolean();

  const handleSuccess = (requestId?: string) => {
    toast.success("Request submitted successfully!");
    if (requestId) {
      router.push(`/requests/${requestId}`);
    } else {
      router.push("/requests"); // Fallback if no ID
    }
  };

  return (
    <div className="min-h-screen  ">
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      <div className="max-w-7xl mx-auto my-28 ">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Left Column - Info on larger screens, hidden on mobile */}
          <div className="hidden md:block md:col-span-1">
            <Card className="h-full shadow-md backdrop-blur-sm bg-white/90 dark:bg-slate-800/90 border-0">
              <CardHeader>
                <CardTitle className="text-2xl font-bold text-green-600 dark:text-green-400">
                  Create Your Perfect Dish
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300">
                  Tell us what you'd like to eat today
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                    Use this form to request a custom dish from our hosts.
                    Whether it's a special occasion or you're just craving
                    something specific, we'll do our best to accommodate your
                    request.
                  </p>

                  <div className="border-l-4 border-green-500 pl-4 py-2 space-y-2">
                    <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                      How it works:
                    </p>
                    <ol className="list-decimal list-inside text-sm text-gray-700 dark:text-gray-300 space-y-1">
                      <li>Fill out the dish details</li>
                      <li>Choose your serving options</li>
                      <li>Submit your request</li>
                      <li>We'll confirm availability</li>
                    </ol>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Form */}
          <div className="col-span-1 md:col-span-2">
            <Card className="shadow-lg backdrop-blur-sm bg-white/90 dark:bg-slate-800/90 border-0">
              <CardHeader className="pb-6 border-b border-gray-200 dark:border-gray-700">
                <CardTitle className="text-2xl font-bold text-gray-800 dark:text-white">
                  Dish Request Form
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-300">
                  Fill out the details below to create your custom dish request
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-6">
                {isMobile ? (
                  <Tabs defaultValue="form" className="mb-6">
                    <TabsList className="w-full">
                      <TabsTrigger value="form" className="flex-1">
                        Form
                      </TabsTrigger>
                      <TabsTrigger value="info" className="flex-1">
                        Info
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="form">
                      <ScrollArea className="h-[70vh]">
                        <DishRequestForm onSuccess={handleSuccess} />
                      </ScrollArea>
                    </TabsContent>
                    <TabsContent value="info">
                      <div className="space-y-4 p-2">
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          Use this form to request a custom dish from our
                          kitchen. Whether it's a special occasion or you're
                          just craving something specific, we'll do our best to
                          accommodate your request.
                        </p>

                        <div className="border-l-4 border-indigo-500 pl-4 py-2">
                          <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                            How it works:
                          </p>
                          <ol className="list-decimal list-inside text-sm text-gray-700 dark:text-gray-300 mt-2 space-y-1">
                            <li>Fill out the dish details</li>
                            <li>Choose your serving options</li>
                            <li>Submit your request</li>
                            <li>We'll confirm availability</li>
                          </ol>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                ) : (
                  <DishRequestForm onSuccess={handleSuccess} />
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

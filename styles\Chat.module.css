/* Chat Layout */
.chatLayout {
  display: flex;
  height: calc(100vh - 64px); /* Adjust based on your navbar height */
  width: 100%;
  background-color: #f9f9f9;
}

.sidebar {
  width: 320px;
  border-right: 1px solid #e0e0e0;
  background-color: #fff;
  overflow-y: auto;
}

.chatWindow {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f9f9f9;
}

.noConversation {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-size: 16px;
  text-align: center;
  padding: 20px;
}

.loading,
.error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
  font-size: 16px;
  padding: 20px;
}

.error {
  color: #e53935;
}

/* Conversation List */
.conversationList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.manageButton {
  background: none;
  border: none;
  color: #2196f3;
  font-size: 14px;
  cursor: pointer;
}

.searchContainer {
  position: relative;
  padding: 12px 16px;
  border-bottom: 1px solid #e0e0e0;
}

.searchInput {
  width: 100%;
  padding: 10px 16px 10px 36px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
}

.searchIcon {
  position: absolute;
  left: 28px;
  top: 22px;
  color: #9e9e9e;
}

.conversations {
  flex: 1;
  overflow-y: auto;
}

.conversationItem {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.conversationItem:hover {
  background-color: #f5f5f5;
}

.conversationItem.active {
  background-color: #e3f2fd;
}

.conversationItem.archived {
  opacity: 0.7;
}

.avatar {
  position: relative;
  margin-right: 12px;
}

.avatarImage {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.defaultAvatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #2196f3;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
}

.statusIndicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #4caf50;
  border: 2px solid white;
}

.conversationInfo {
  flex: 1;
  min-width: 0; /* Ensures text truncation works */
}

.conversationHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.conversationHeader h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.time {
  font-size: 12px;
  color: #9e9e9e;
  white-space: nowrap;
}

.conversationPreview {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversationPreview p {
  margin: 0;
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: #f0f0f0;
  color: #666;
}

.noConversations {
  padding: 20px;
  text-align: center;
  color: #666;
}

/* Chat Window */
.chatWindowContainer {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chatHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.participantInfo {
  display: flex;
  align-items: center;
}

.participantDetails {
  margin-left: 12px;
}

.participantDetails h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.status {
  font-size: 12px;
  color: #9e9e9e;
}

.chatActions {
  display: flex;
  align-items: center;
}

.orderButton {
  background: none;
  border: none;
  color: #2196f3;
  font-size: 14px;
  cursor: pointer;
  margin-right: 16px;
}

.moreButton {
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.moreButton:hover {
  background-color: #f0f0f0;
}

.moreIcon {
  font-size: 20px;
  color: #666;
}

.messagesContainer {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
}

.messagesContainer::-webkit-scrollbar {
  width: 8px;
}

.messagesContainer::-webkit-scrollbar-track {
  background: transparent;
}

.messagesContainer::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}

.messagesContainer::-webkit-scrollbar-thumb:hover {
  background-color: #999;
}

.messageGroup {
  margin-bottom: 16px;
}

.dateHeader {
  text-align: center;
  margin: 16px 0;
  position: relative;
}

.dateHeader span {
  background-color: #f9f9f9;
  padding: 0 10px;
  font-size: 12px;
  color: #9e9e9e;
  position: relative;
  z-index: 1;
}

.dateHeader::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e0e0e0;
  z-index: 0;
}

.typingIndicator {
  font-size: 12px;
  color: #9e9e9e;
  margin: 8px 0;
  font-style: italic;
}

.noMessages {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 16px;
  text-align: center;
}

/* Chat Message */
.messageContainer {
  display: flex;
  margin-bottom: 16px;
  max-width: 80%;
}

.ownMessage {
  margin-left: auto;
  flex-direction: row-reverse;
}

.otherMessage {
  margin-right: auto;
}

.messageAvatar {
  margin: 0 8px;
  align-self: flex-end;
}

.messageAvatar .avatarImage,
.messageAvatar .defaultAvatar {
  width: 32px;
  height: 32px;
  font-size: 14px;
}

.messageContent {
  display: flex;
  flex-direction: column;
}

.messageBubble {
  padding: 10px 12px;
  border-radius: 16px;
  font-size: 14px;
  max-width: 100%;
  word-wrap: break-word;
}

.ownMessage .messageBubble {
  background-color: #2196f3;
  color: white;
  border-bottom-right-radius: 4px;
}

.otherMessage .messageBubble {
  background-color: #fff;
  color: #333;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.messageInfo {
  display: flex;
  align-items: center;
  margin-top: 4px;
}

.ownMessage .messageInfo {
  justify-content: flex-end;
}

.messageTime {
  font-size: 11px;
  color: #9e9e9e;
}

.readStatus {
  margin-left: 4px;
  font-size: 12px;
  color: #9e9e9e;
  font-weight: bold;
  transition: color 0.2s ease;
}

.readStatus.read {
  color: #2196f3;
}

.readStatus:hover {
  opacity: 0.8;
}

.systemMessage {
  text-align: center;
  margin: 16px 0;
}

.systemMessage p {
  display: inline-block;
  margin: 0;
  padding: 6px 12px;
  background-color: #f0f0f0;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
}

.systemMessage .messageTime {
  display: block;
  margin-top: 4px;
}

.attachments {
  margin-top: 8px;
}

.imageAttachment {
  margin-top: 8px;
  border-radius: 8px;
  overflow: hidden;
}

.attachmentImage {
  width: 100%;
  height: auto;
  object-fit: contain;
}

.fileAttachment {
  display: flex;
  align-items: center;
  margin-top: 8px;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
}

.fileIcon {
  margin-right: 8px;
}

.fileName {
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Chat Input */
.chatInputContainer {
  padding: 12px 16px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
}

.attachmentPreview {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.attachmentItem {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f0f0f0;
  border-radius: 4px;
  font-size: 12px;
}

.attachmentName {
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.removeAttachment {
  background: none;
  border: none;
  color: #666;
  margin-left: 4px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}

.inputWrapper {
  display: flex;
  align-items: center;
  background-color: #f0f0f0;
  border-radius: 24px;
  padding: 4px;
}

.attachButton,
.sendButton {
  background: none;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
}

.attachButton:hover,
.sendButton:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.attachButton:disabled,
.sendButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.fileInput {
  display: none;
}

.messageInput {
  flex: 1;
  border: none;
  background: none;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
}

.messageInput:disabled {
  opacity: 0.7;
}

.sendButton {
  color: #2196f3;
}

.sendIcon {
  transform: rotate(90deg);
}

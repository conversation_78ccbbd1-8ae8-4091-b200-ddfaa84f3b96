"use client";

import React, { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import {
  getRequestById,
  respondToRequest,
  getResponsesForRequest, // Corrected function name
  acceptResponse as acceptResponseApi,
} from "@/api/requests";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
// import { useSocket } from "@/context/hooks/use-socket-hook"; // WebSocket removed
import { paths } from "@/routes/paths";
import Image from "next/image";
import { format } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  GoogleMap,
  LoadScript,
  Marker as GoogleMarker,
  InfoWindow,
} from "@react-google-maps/api";
// import "leaflet/dist/leaflet.css"; // Removed
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import {
  MapPin,
  Clock,
  Calendar,
  Users,
  ChefHat,
  ArrowLeft,
  Share2,
  MessageCircle,
} from "lucide-react";
import { HeaderBase } from "@/components/layouts/core/header-base";
import { useBoolean } from "@/hooks/use-boolean";
import { Dish as HostDish } from "@/types/host"; // Renamed to avoid conflict
import AddDish from "@/components/addDish";
import {
  getAllCuisines,
  getAllIngredients,
  getPremadeDishes,
  getHostProfile,
} from "@/api/host";
import { DiningLocations } from "@/types/host";
import RespondWithDishButton from "@/components/request/respond-with-dish-button";

// --- START TYPE DEFINITIONS (based on request_response_doc.md) ---
interface ApiLocation {
  _id?: string;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  coordinates?: [number, number]; // Typically [longitude, latitude]
  postalCode?: string;
  latitude?: number; // Added for map usage
  longitude?: number; // Added for map usage
}

interface ApiUser {
  _id: string;
  name?: string;
  avatar?: string;
}

interface ApiDishRequest {
  title: string;
  ingredients: string[];
  description?: string;
  offering: string;
  minServing: number;
  maxServing: number;
  quantity?: number;
  offeringTime?: string; // ISO Date string
}

interface ApiRequest {
  _id: string;
  user: ApiUser;
  dishRequest: ApiDishRequest;
  status:
    | "pending_responses"
    | "confirmed"
    | "closed"
    | "assigned"
    | "active"
    | "open"; // Harmonize these
  location: ApiLocation;
  city?: string;
  responseCount?: number;
  expiresAt?: string | null;
  createdAt: string;
  updatedAt: string;
  acceptedResponseId?: string | ApiAcceptedResponseData; // Can be ID or populated
  description?: string; // From current page, doc has it in dishRequest
  // offering?: string; // This is in dishRequest
  // offeringTime?: string; // This is in dishRequest
}

interface ApiDishForResponse {
  _id: string;
  name: string;
  price: number;
  photos?: string[];
  description?: string;
}

interface ApiAcceptedResponseData {
  _id: string;
  host: ApiUser;
  dish: ApiDishForResponse;
  message: string;
  status: string;
}

interface ApiResponse {
  _id: string;
  request: string; // Request ID
  host: ApiUser;
  dish?: ApiDishForResponse; // Main dish details for the response
  message: string;
  status: "pending_user_action" | "accepted_by_user" | "declined_by_user";
  createdAt: string;
  price?: number; // Price for this specific response/offer
  estimatedDeliveryTime?: string; // ISO Date string
}
// --- END TYPE DEFINITIONS ---

// Validation schema for response form
const responseSchema = z.object({
  price: z.number().min(1, "Price must be at least 1"),
  message: z.string().min(10, "Message must be at least 10 characters"),
  estimatedDeliveryTime: z.date().optional(),
  dish: z.string().optional(), // ID of an existing dish if selected
});

type ResponseFormData = z.infer<typeof responseSchema>;

interface RequestDetailsPageProps {
  params: {
    id: string;
  };
}

const POLLING_INTERVAL = 10000; // 10 seconds for polling

const RequestDetailsPage = () => {
  const params = useParams();
  const requestId = params.id as string;
  const router = useRouter();
  const { user: currentUser, host } = useAuthContext(); // Assuming 'user' is the logged-in user
  console.log("Host ========> ", host);
  // const socket = useSocket(); // WebSocket removed

  const [request, setRequest] = useState<ApiRequest | null>(null);
  const [responses, setResponses] = useState<ApiResponse[]>([]);
  const [userRole, setUserRole] = useState<"requestor" | "host" | "viewer">(
    "viewer"
  );
  const [hostDishes, setHostDishes] = useState<HostDish[]>([]); // Existing dishes of the host
  const [loading, setLoading] = useState(true);
  const [loadingResponses, setLoadingResponses] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [acceptingResponse, setAcceptingResponse] = useState(false);
  const hamburgerMenu = useBoolean();
  const [infoWindowOpen, setInfoWindowOpen] = useState(false); // Moved here

  const [ingredients, setIngredients] = useState([]);
  const [cuisines, setCuisines] = useState([]);
  const [premadeDishes, setPremadeDishes] = useState([]); // This seems related to host's general dishes, not specific to this request flow
  const [diningLocations, setDiningLocations] = useState<DiningLocations[]>([]);

  const form = useForm<ResponseFormData>({
    resolver: zodResolver(responseSchema),
    defaultValues: {
      price: 0,
      message: "",
      estimatedDeliveryTime: undefined,
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [ingredientsRes, cuisinesRes, dishesRes, locationsRes] =
          await Promise.all([
            getAllIngredients(),
            getAllCuisines(),
            getPremadeDishes(),
            getHostProfile(),
          ]);

        setIngredients(
          ingredientsRes.data.map((i: { _id: string; name: string }) => ({
            value: i._id,
            label: i.name,
          }))
        );
        setCuisines(cuisinesRes.data);
        setPremadeDishes(dishesRes.data);
        setDiningLocations(locationsRes.data.diningLocations);
      } catch (error) {
        toast.error("Failed to fetch required data");
      }
    };

    fetchData();
  }, []);

  // Fetch initial request details
  const fetchRequestDetails = useCallback(async () => {
    if (!requestId) return;
    // setLoading(true); // Keep main loading for initial, subsequent polls won't set this
    try {
      const data = await getRequestById(requestId);
      setRequest(data);
    } catch (error) {
      console.error("Error fetching request details:", error);
      toast.error("Failed to load request details. Please try again.");
    } finally {
      setLoading(false); // Only set to false on initial load
    }
  }, [requestId]);

  useEffect(() => {
    fetchRequestDetails();
  }, [fetchRequestDetails]);

  // Determine user role
  useEffect(() => {
    if (request) {
      if (request.user._id === currentUser?._id) {
        setUserRole("requestor");
      } else if (host) {
        // Check if current user is a host
        setUserRole("host");
      } else {
        setUserRole("viewer");
      }
    }
  }, [request, currentUser, host]);
  console.log("User role", userRole);

  // Polling for request updates
  useEffect(() => {
    if (!requestId || !request) return; // Don't poll if no request ID or initial request not loaded

    const intervalId = setInterval(async () => {
      try {
        const updatedRequestData = await getRequestById(requestId);
        setRequest(updatedRequestData);
      } catch (error) {
        console.error("Polling: Error fetching request details:", error);
        // Optionally, show a non-intrusive error for polling failures
      }
    }, POLLING_INTERVAL);

    return () => clearInterval(intervalId);
  }, [requestId, request]); // Re-run if request object itself changes, to restart polling if needed

  // Fetch responses if user is requestor
  const fetchResponsesForRequestor = useCallback(async () => {
    if (userRole === "requestor" && requestId) {
      setLoadingResponses(true);
      try {
        const responseData = await getResponsesForRequest(requestId); // Corrected function call
        setResponses(responseData.data || []);
      } catch (error) {
        console.error("Error fetching responses:", error);
        toast.error("Failed to load responses.");
      } finally {
        setLoadingResponses(false);
      }
    }
  }, [userRole, requestId]);

  useEffect(() => {
    fetchResponsesForRequestor();
  }, [fetchResponsesForRequestor]);

  // Polling for responses if user is requestor
  useEffect(() => {
    if (userRole !== "requestor" || !requestId) return;

    const intervalId = setInterval(async () => {
      try {
        const updatedResponsesData = await getResponsesForRequest(requestId); // Corrected function call
        setResponses(updatedResponsesData.data || []);
      } catch (error) {
        console.error("Polling: Error fetching responses:", error);
      }
    }, POLLING_INTERVAL);

    return () => clearInterval(intervalId);
  }, [userRole, requestId]);

  useEffect(() => {
    if (host?.dishes) {
      setHostDishes(host.dishes);
    }
  }, [host]);

  // WebSocket removed:
  // useEffect(() => {
  //   if (socket && request) {
  //     socket.on("request:accepted", ({ requestId: updatedRequestId }) => {
  //       if (updatedRequestId === requestId) {
  //         getRequestById(requestId)
  //           .then((data) => setRequest(data))
  //           .catch((error) =>
  //             console.error("Error refreshing request:", error)
  //           );
  //       }
  //     });
  //     return () => {
  //       socket.off("request:accepted");
  //     };
  //   }
  // }, [socket, requestId, request]);

  // Submit response to the request (Host action)
  const onSubmit = async (data: ResponseFormData) => {
    setSubmitting(true);
    try {
      // Convert Date to string for API
      const responseData = {
        ...data,
        estimatedDeliveryTime: data.estimatedDeliveryTime
          ? data.estimatedDeliveryTime.toISOString()
          : undefined,
      };
      await respondToRequest(requestId, responseData);
      toast.success("Response submitted successfully");
      // Optionally, refresh responses or navigate
      // For now, polling will update the request details (e.g., responseCount)
      // router.push(paths.host.responses); // Consider if still needed
      form.reset(); // Reset form after submission
      fetchRequestDetails(); // Re-fetch request to update response count immediately
      if (userRole === "requestor") fetchResponsesForRequestor(); // If somehow a requestor is submitting (should not happen with this form)
    } catch (error: any) {
      console.error("Error submitting response:", error);
      toast.error(
        error?.response?.data?.message ||
          "Failed to submit response. Please try again."
      );
    } finally {
      setSubmitting(false);
    }
  };

  // Accept a response (Requestor action)
  const handleAcceptResponse = async (responseId: string) => {
    if (!request) return;
    setAcceptingResponse(true);
    try {
      const updatedRequest = await acceptResponseApi(request._id, responseId);
      setRequest(updatedRequest); // Update local request state
      toast.success("Response accepted successfully!");
      // Polling will eventually update, or force refresh:
      fetchRequestDetails();
      fetchResponsesForRequestor(); // Refresh responses list
    } catch (error: any) {
      console.error("Error accepting response:", error);
      toast.error(
        error?.response?.data?.message ||
          "Failed to accept response. Please try again."
      );
    } finally {
      setAcceptingResponse(false);
    }
  };

  const goBack = () => {
    router.back();
  };

  if (loading) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={goBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back
          </Button>
          <Skeleton className="h-8 w-1/2" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-8 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />

                <div className="flex flex-wrap gap-2 my-4">
                  {[...Array(5)].map((_, i) => (
                    <Skeleton key={i} className="h-6 w-16 rounded-full" />
                  ))}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                  <Skeleton className="h-6 w-full" />
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-full" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full mb-4" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (!request) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        <div className="text-center py-12">
          <h3 className="text-lg font-medium mb-2">Request not found</h3>
          <p className="text-gray-500">
            The request you&apos;re looking for doesn&apos;t exist or has been
            removed
          </p>
          <Button className="mt-4" onClick={goBack}>
            Back to Feed
          </Button>
        </div>
      </div>
    );
  }

  // Determine if the current user can respond (is a host and not the requestor, and request is open)
  const canRespond =
    userRole === "host" && request.status === "pending_responses"; // Or "open" if that's the active status

  // Determine if the request is still open for responses generally
  const isRequestOpen =
    request.status === "pending_responses" ||
    request.status === "open" ||
    request.status === "active";
  // TODO: Harmonize status values based on backend. Assuming "pending_responses" is the primary open state.

  const hasLocationData =
    request.location && request.location.latitude && request.location.longitude;

  const mapCenter = hasLocationData
    ? {
        lat: request.location.latitude || 0,
        lng: request.location.longitude || 0,
      }
    : { lat: 39.7392, lng: -104.9903 }; // Default to Denver
  // const [infoWindowOpen, setInfoWindowOpen] = useState(false); // Removed duplicate
  const GOOGLE_MAPS_API_KEY =
    process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY ||
    "YOUR_GOOGLE_MAPS_API_KEY_HERE"; // Ensure this key is configured
  return (
    <>
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      <div className="container mx-auto py-6 max-w-4xl mt-24">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={goBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back
          </Button>
          <h1 className="text-2xl font-bold">Request Details</h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-2xl">
                      {request.dishRequest.title}
                    </CardTitle>
                    <CardDescription className="flex items-center mt-1">
                      <MapPin className="h-4 w-4 mr-1" />
                      {request.location.city}, {request.location.state}
                    </CardDescription>
                  </div>
                  <Badge
                    variant={
                      request.status === "pending_responses" ||
                      request.status === "open" ||
                      request.status === "active" ||
                      request.status === "confirmed" ||
                      request.status === "assigned"
                        ? "default"
                        : "secondary"
                    }
                    className={
                      request.status === "pending_responses" ||
                      request.status === "open" ||
                      request.status === "active"
                        ? "bg-green-500 text-white capitalize"
                        : request.status === "confirmed" ||
                          request.status === "assigned"
                        ? "bg-blue-500 text-white capitalize"
                        : "capitalize" // Ensures text is capitalized if no other style applies
                    }
                  >
                    {request.status.charAt(0).toUpperCase() +
                      request.status.slice(1).replace("_", " ")}
                  </Badge>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <div className="flex items-center">
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarImage src={request.user.avatar} />
                    <AvatarFallback>
                      {request.user.name?.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{request.user.name}</p>
                    <p className="text-sm text-gray-500">
                      Posted {format(new Date(request.createdAt), "PPP")}
                    </p>
                  </div>
                </div>

                {request.description && (
                  <div>
                    <h3 className="font-medium mb-1">Description</h3>
                    <p className="text-gray-700">{request.description}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-medium mb-2">Ingredients</h3>
                  <div className="flex flex-wrap gap-2">
                    {request.dishRequest.ingredients?.map(
                      (ingredient: string, idx: number) => (
                        <Badge
                          key={idx}
                          variant="secondary"
                          className="rounded-full"
                        >
                          {ingredient}
                        </Badge>
                      )
                    ) || (
                      <p className="text-sm text-gray-500">
                        No ingredients listed.
                      </p>
                    )}
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <ChefHat className="h-5 w-5 mr-2 text-gray-500" />
                    <div>
                      <p className="font-medium">Offering Type</p>
                      <p>{request.dishRequest.offering}</p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <Users className="h-5 w-5 mr-2 text-gray-500" />
                    <div>
                      <p className="font-medium">Servings</p>
                      <p>
                        {request.dishRequest.minServing} -{" "}
                        {request.dishRequest.maxServing} people
                      </p>
                    </div>
                  </div>

                  {/* Corrected offeringTime display with checks */}
                  {request.dishRequest.offeringTime ? (
                    <>
                      <div className="flex items-center">
                        <Calendar className="h-5 w-5 mr-2 text-gray-500" />
                        <div>
                          <p className="font-medium">Date</p>
                          <p>
                            {format(
                              new Date(request.dishRequest.offeringTime),
                              "PPP"
                            )}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-5 w-5 mr-2 text-gray-500" />
                        <div>
                          <p className="font-medium">Time</p>
                          <p>
                            {format(
                              new Date(request.dishRequest.offeringTime),
                              "p"
                            )}
                          </p>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex items-center">
                      <Calendar className="h-5 w-5 mr-2 text-gray-400" />{" "}
                      {/* Indicate optional nature */}
                      <div>
                        <p className="font-medium text-gray-500">Date & Time</p>
                        <p className="text-gray-400">Not specified</p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>

              <CardFooter className="flex justify-between items-center border-t pt-4">
                {/* Share Button - Assuming it stays or is handled elsewhere if not needed */}
                {/* <Button variant="outline" size="sm" className="flex items-center">
                  <Share2 className="h-4 w-4 mr-1" /> Share
                </Button> */}

                {/* Action Button: Respond / View Responses */}
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center w-full" // Make it full width if it's the primary action here
                  onClick={() => {
                    if (userRole === "host" && canRespond) {
                      form.setFocus("message"); // Or scroll to response form
                    } else if (userRole === "requestor") {
                      // Always navigate to the dedicated responses page if requestor
                      router.push(`/requests/${requestId}/responses`);
                    }
                  }}
                  disabled={
                    userRole === "viewer" ||
                    (userRole === "host" && !canRespond)
                    // Requestor button to view responses should generally be enabled,
                    // the responses page will handle "no responses yet" state.
                  }
                >
                  <MessageCircle className="h-4 w-4 mr-1" />
                  {userRole === "host" && canRespond
                    ? "Respond to Request"
                    : userRole === "requestor"
                    ? responses.length > 0
                      ? `View ${responses.length} Response(s)`
                      : "View Responses"
                    : "Details"}{" "}
                  {/* Fallback for viewer or other roles */}
                </Button>
              </CardFooter>
            </Card>

            {/* --- REQUESTOR VIEW: Summary Link to Detailed Responses Page --- */}
            {userRole === "requestor" && (
              <div id="responses-section-summary" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Responses Summary</CardTitle>
                    <CardDescription>
                      Manage and review responses from hosts.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {loadingResponses && (
                      <Skeleton className="h-5 w-3/4 mb-2" />
                    )}
                    {!loadingResponses && responses.length > 0 && (
                      <p>
                        You have received <strong>{responses.length}</strong>{" "}
                        response(s) for this request.
                      </p>
                    )}
                    {!loadingResponses && responses.length === 0 && (
                      <p className="text-gray-500">
                        No responses have been received yet.
                        {(request.status === "pending_responses" ||
                          request.status === "open" ||
                          request.status === "active") &&
                          " We are actively polling for new responses."}
                      </p>
                    )}
                  </CardContent>
                  <CardFooter>
                    <Button
                      onClick={() =>
                        router.push(`/requests/${requestId}/responses`)
                      }
                      className="w-full"
                      disabled={loadingResponses && responses.length === 0} // Disable if loading and no responses yet
                    >
                      {responses.length > 0
                        ? "View All Detailed Responses"
                        : "Check for Responses"}
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            )}
            {/* --- END REQUESTOR VIEW: Summary Link --- */}
          </div>

          {/* Right Column: Location and Host Response Form */}
          <div>
            <Card className="mb-6">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Location</CardTitle>
              </CardHeader>
              <CardContent>
                {hasLocationData ? (
                  <div className="h-48 rounded-md overflow-hidden mb-3">
                    {GOOGLE_MAPS_API_KEY &&
                    GOOGLE_MAPS_API_KEY !== "YOUR_GOOGLE_MAPS_API_KEY_HERE" ? (
                      <LoadScript googleMapsApiKey={GOOGLE_MAPS_API_KEY}>
                        <GoogleMap
                          mapContainerStyle={{ height: "100%", width: "100%" }}
                          center={mapCenter}
                          zoom={13}
                          options={{
                            scrollwheel: false,
                            streetViewControl: false,
                            mapTypeControl: false,
                            fullscreenControl: false,
                            zoomControl: false,
                            clickableIcons: false,
                            styles: [
                              {
                                featureType: "poi",
                                elementType: "labels",
                                stylers: [{ visibility: "off" }],
                              },
                            ],
                          }}
                        >
                          {hasLocationData && (
                            <GoogleMarker
                              position={mapCenter}
                              onClick={() => setInfoWindowOpen(!infoWindowOpen)} // Toggle InfoWindow
                            >
                              {infoWindowOpen && (
                                <InfoWindow
                                  onCloseClick={() => setInfoWindowOpen(false)}
                                >
                                  <div>
                                    <p>{request.location.street}</p>
                                    <p>
                                      {request.location.city},{" "}
                                      {request.location.state}{" "}
                                      {request.location.postalCode}
                                    </p>
                                  </div>
                                </InfoWindow>
                              )}
                            </GoogleMarker>
                          )}
                        </GoogleMap>
                      </LoadScript>
                    ) : (
                      <div className="bg-gray-200 h-full flex items-center justify-center">
                        <p className="text-gray-600 text-center p-4">
                          Google Maps API key is not configured. Please set the
                          NEXT_PUBLIC_GOOGLE_MAPS_API_KEY environment variable
                          or update the placeholder in the code.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="bg-gray-100 h-48 flex items-center justify-center rounded-md mb-3">
                    <p className="text-gray-500">Location map not available</p>
                  </div>
                )}

                <p className="text-sm">
                  {request.location.street}
                  <br />
                  {request.location.city}, {request.location.state}{" "}
                  {request.location.postalCode}
                  <br />
                  {request.location.country}
                </p>
              </CardContent>
            </Card>

            {/* --- START HOST VIEW: RESPOND TO REQUEST / OTHER MESSAGES --- */}
            {userRole === "host" && request && (
              <>
                {isRequestOpen && canRespond ? (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">
                        Respond to Request
                      </CardTitle>
                      <CardDescription>
                        Choose how you want to respond. You can offer one of
                        your existing dishes or propose a custom offer.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <RespondWithDishButton
                        requestId={request._id}
                        offering={request.dishRequest.offering}
                      />
                      {hostDishes.length > 0 && ( // Corrected to hostDishes
                        <div className="mt-4">
                          <h3 className="text-sm font-medium mb-2">
                            Or make an offer with an existing dish / custom
                            message
                          </h3>
                          <Form {...form}>
                            <form
                              onSubmit={form.handleSubmit(onSubmit)}
                              className="space-y-4"
                            >
                              <FormField
                                control={form.control}
                                name="price"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Your Price ($)</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        min="1"
                                        step="0.01"
                                        placeholder="Enter your price"
                                        {...field}
                                        onChange={(e) =>
                                          field.onChange(
                                            parseFloat(e.target.value)
                                          )
                                        }
                                        disabled={submitting} // Removed !canRespond as parent condition handles it
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              {request.dishRequest.offering !== "Dine In" && (
                                <FormField
                                  control={form.control}
                                  name="estimatedDeliveryTime"
                                  render={({ field }) => (
                                    <FormItem>
                                      {" "}
                                      {/* Removed flex flex-col, DateTimePicker handles layout */}
                                      <FormLabel>
                                        Estimated Delivery/Pickup Time
                                      </FormLabel>
                                      <FormControl>
                                        <DateTimePicker
                                          date={field.value} // Assuming DateTimePicker takes 'date'
                                          setDate={field.onChange} // Assuming DateTimePicker takes 'setDate'
                                        />
                                      </FormControl>
                                      <FormDescription>
                                        When can you deliver or have the food
                                        ready for pickup?
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                              <FormField
                                control={form.control}
                                name="message"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Message to Customer</FormLabel>
                                    <FormControl>
                                      <Textarea
                                        placeholder="Describe what you can offer, special notes, or ask questions"
                                        className="min-h-24"
                                        {...field}
                                        disabled={submitting}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              {hostDishes.length > 0 && ( // Corrected to hostDishes
                                <FormField
                                  control={form.control}
                                  name="dish"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormLabel>Select Dish</FormLabel>
                                      <FormControl>
                                        <select
                                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                          {...field}
                                          disabled={submitting}
                                        >
                                          <option value="">
                                            Select a dish...
                                          </option>
                                          {hostDishes.map(
                                            (
                                              dish // Corrected to hostDishes
                                            ) => (
                                              <option
                                                key={dish._id}
                                                value={dish._id}
                                              >
                                                {dish.name}
                                              </option>
                                            )
                                          )}
                                        </select>
                                      </FormControl>
                                      <FormDescription>
                                        Choose a dish from your menu to fulfill
                                        this request
                                      </FormDescription>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              )}
                              <Button
                                type="submit"
                                className="w-full"
                                disabled={submitting}
                              >
                                {submitting ? (
                                  <>
                                    <span className="animate-spin mr-2">
                                      ⚙️
                                    </span>
                                    Submitting...
                                  </>
                                ) : (
                                  "Submit Offer"
                                )}
                              </Button>
                            </form>
                          </Form>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ) : (
                  // This is for host, but request is NOT open or they CANNOT respond
                  <Card>
                    <CardContent className="pt-6">
                      <p className="text-gray-500 font-medium text-center">
                        This request is currently not open for new responses
                        (Status: {request.status.replace("_", " ")}).
                      </p>
                      <div className="text-center mt-4">
                        <Button
                          onClick={() => router.push(paths.host.requests)}
                        >
                          Browse More Requests
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}

            {userRole === "viewer" && (
              <Card>
                <CardContent className="pt-6">
                  <p className="text-gray-500 text-center">
                    Login as a host to respond to requests, or as the requestor
                    to see responses.
                  </p>
                </CardContent>
              </Card>
            )}
            {/* --- END HOST VIEW: RESPOND TO REQUEST / OTHER MESSAGES --- */}
          </div>
        </div>
      </div>
    </>
  );
};

export default RequestDetailsPage;

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { getHostResponses } from "@/api/requests";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { format, formatDistanceToNow } from "date-fns";
import { paths } from "@/routes/paths";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import {
  MessageCircle,
  CheckCircle2,
  XCircle,
  Clock,
  Hourglass,
  Receipt,
  Ban,
  ChefHat,
} from "lucide-react";

interface HostResponse {
  _id: string;
  request: {
    _id: string;
    dishRequest: {
      title: string;
    };
  };
  host: string; // Host ID
  dish: {
    _id: string;
    name: string;
    price: number;
  };
  message: string;
  status: string; // e.g., "pending_user_action", "accepted_by_user"
  createdAt: string;
  updatedAt?: string;
}

const HostResponsesPage = () => {
  const router = useRouter();
  const { host } = useAuthContext();

  const [loading, setLoading] = useState(true);
  const [responses, setResponses] = useState<HostResponse[]>([]);
  const [activeTab, setActiveTab] = useState("all");

  const fetchResponses = useCallback(async () => {
    // setLoading(true); // Set loading only if not polling or for initial load
    try {
      const fetchedResponses = await getHostResponses();
      setResponses(fetchedResponses.data);
    } catch (error) {
      console.error("Error fetching host responses:", error);
      toast.error("Failed to fetch your responses. Please try again later.");
    } finally {
      setLoading(false); // Ensure loading is false after fetch
    }
  }, []);

  useEffect(() => {
    if (host?._id) {
      setLoading(true); // Set loading true for initial fetch
      fetchResponses(); // Initial fetch
      const intervalId = setInterval(fetchResponses, 15000); // Poll every 15 seconds
      return () => clearInterval(intervalId); // Cleanup on unmount
    }
  }, [host?._id, fetchResponses]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending_user_action":
        return "bg-blue-100 text-blue-800";
      case "accepted_by_user":
        return "bg-green-100 text-green-800";
      case "rejected_by_user": // Assuming this status might exist
      case "rejected":
        return "bg-red-100 text-red-800";
      case "completed":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending_user_action":
        return <Hourglass className="h-4 w-4 mr-1" />;
      case "accepted_by_user":
        return <CheckCircle2 className="h-4 w-4 mr-1" />;
      case "rejected_by_user":
      case "rejected":
        return <XCircle className="h-4 w-4 mr-1" />;
      case "completed":
        return <Receipt className="h-4 w-4 mr-1" />;
      case "cancelled":
        return <Ban className="h-4 w-4 mr-1" />;
      default:
        return <Clock className="h-4 w-4 mr-1" />;
    }
  };

  const handleViewResponse = (requestId: string) => {
    router.push(`${paths.host.requestDetails}/${requestId}`);
  };

  const renderResponseItem = (response: HostResponse) => {
    const displayStatus = response.status.replace(/_/g, " ");

    return (
      <Card
        key={response._id}
        className="mb-4 hover:shadow-lg transition-shadow duration-200 ease-in-out border border-gray-200 rounded-lg overflow-hidden"
      >
        <CardHeader className="pb-3 bg-gray-50 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-lg font-semibold text-gray-800">
                Request: {response.request.dishRequest.title}
              </CardTitle>
              <CardDescription className="text-xs text-gray-500 mt-1">
                Response ID: {response._id}
              </CardDescription>
            </div>
            <span
              className={`text-xs font-semibold rounded-full px-3 py-1 flex items-center ${getStatusColor(
                response.status
              )}`}
            >
              {getStatusIcon(response.status)}
              {displayStatus.charAt(0).toUpperCase() + displayStatus.slice(1)}
            </span>
          </div>
        </CardHeader>

        <CardContent className="py-4 px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm mb-3">
            <div>
              <p className="font-medium text-gray-700">Your Offer Price</p>
              <p className="text-green-600 font-bold text-lg">
                ${response.dish.price.toFixed(2)}
              </p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Offered Dish</p>
              <p className="text-gray-800">{response.dish.name}</p>
            </div>
          </div>

          <div className="text-sm">
            <p className="font-medium text-gray-700">Your Message to User</p>
            <p className="text-gray-600 line-clamp-3 bg-gray-50 p-2 rounded">
              {response.message}
            </p>
          </div>
        </CardContent>

        <CardFooter className="pt-3 pb-3 px-4 flex justify-between items-center border-t bg-gray-50">
          <div className="text-xs text-gray-500">
            Response sent{" "}
            {formatDistanceToNow(new Date(response.createdAt), {
              addSuffix: true,
            })}
          </div>

          <Button
            variant="outline"
            size="sm"
            className="flex items-center text-blue-600 border-blue-600 hover:bg-blue-50"
            onClick={() => handleViewResponse(response.request._id)}
          >
            <MessageCircle className="h-4 w-4 mr-1.5" />
            {response.status.toLowerCase() === "accepted_by_user"
              ? "View Order Details"
              : "View Original Request"}
          </Button>
        </CardFooter>
      </Card>
    );
  };

  const renderSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, idx) => (
        <Card key={idx} className="mb-4">
          <CardHeader className="pb-2">
            <div className="flex justify-between">
              <div>
                <Skeleton className="h-5 w-48 mb-2" />
                <Skeleton className="h-4 w-32" />
              </div>
              <Skeleton className="h-6 w-20 rounded-full" />
            </div>
          </CardHeader>
          <CardContent className="pb-2">
            <div className="flex items-center mb-3">
              <Skeleton className="h-8 w-8 rounded-full mr-2" />
              <div>
                <Skeleton className="h-4 w-32 mb-1" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-3">
              <Skeleton className="h-8 w-full" />
              <Skeleton className="h-8 w-full" />
            </div>
            <Skeleton className="h-4 w-full mb-1" />
            <Skeleton className="h-4 w-full" />
          </CardContent>
          <CardFooter className="pt-2 flex justify-between">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-8 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  const filteredResponses = () => {
    if (activeTab === "all") return responses;
    return responses.filter((response) => response.status === activeTab);
  };
  console.log(responses);
  console.log("filteredResponses", filteredResponses());

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Your Responses</h1>
        <Button
          onClick={() => router.push(paths.host.requests)}
          className="flex items-center"
        >
          <ChefHat className="h-4 w-4 mr-2" /> Browse Requests
        </Button>
      </div>

      <Tabs defaultValue="all" onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid grid-cols-3 sm:grid-cols-5 gap-2 mb-8">
          <TabsTrigger value="all" className="px-2 py-1.5 sm:px-4 sm:py-2">
            All
          </TabsTrigger>
          <TabsTrigger
            value="pending_user_action"
            className="px-2 py-1.5 sm:px-4 sm:py-2"
          >
            Pending User
          </TabsTrigger>
          <TabsTrigger
            value="accepted_by_user"
            className="px-2 py-1.5 sm:px-4 sm:py-2"
          >
            Accepted
          </TabsTrigger>
          {/* Assuming these statuses might still be relevant for a host's view of their responses */}
          <TabsTrigger
            value="completed"
            className="px-2 py-1.5 sm:px-4 sm:py-2"
          >
            Completed
          </TabsTrigger>
          <TabsTrigger value="rejected" className="px-2 py-1.5 sm:px-4 sm:py-2">
            Other
          </TabsTrigger>
          {/* "rejected" could cover "rejected_by_user" or "cancelled" if backend uses these */}
        </TabsList>

        <TabsContent value={activeTab}>
          {loading ? (
            renderSkeleton()
          ) : filteredResponses().length > 0 ? (
            <div>{filteredResponses().map(renderResponseItem)}</div>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-lg font-medium mb-2">No responses found</h3>
              <p className="text-gray-500 mb-6">
                {activeTab === "all"
                  ? "You haven't sent any responses yet."
                  : `You don't have any responses with status: ${activeTab.replace(
                      /_/g,
                      " "
                    )}.`}
              </p>
              <Button
                onClick={() => router.push(paths.host.requests)}
                variant="default"
              >
                Browse Available Requests
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HostResponsesPage;

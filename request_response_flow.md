# Request-Response Flow Documentation

This document outlines the typical flow of creating a dish request, responding to it, and accepting a response within the Eats Express system, highlighting the interactions between the client and the server-side components after the removal of WebSocket events.

## Flow Diagram

The following diagram illustrates the sequence of API calls and internal service interactions:

```mermaid
sequenceDiagram
    participant Client
    participant API Gateway/Router
    participant RequestController
    participant RequestService
    participant ResponseService
    participant AddressService
    participant DishModel
    participant RequestModel
    participant ResponseModel

    Client->>API Gateway/Router: POST /requests (Create Request)
    API Gateway/Router->>RequestController: createRequest(req, res)
    RequestController->>AddressService: createAddress(location)
    AddressService-->>RequestController: locationData
    RequestController->>RequestService: createRequest(requestData)
    RequestService->>RequestModel: save(newRequest)
    RequestModel-->>RequestService: savedRequest
    RequestService-->>RequestController: newRequest
    RequestController-->>Client: 201 Created (newRequest)

    Client->>API Gateway/Router: POST /requests/:id/responses (Respond to Request)
    API Gateway/Router->>RequestController: respondToRequest(req, res)
    RequestController->>RequestService: getRequestById(requestId)
    RequestService-->>RequestController: originalRequest
    RequestController->>DishModel: save(newDishData)
    DishModel-->>RequestController: newDish
    RequestController->>ResponseService: createResponse(responsePayload)
    ResponseService->>ResponseModel: save(newResponse)
    ResponseModel-->>ResponseService: savedResponse
    ResponseService-->>RequestController: newResponse
    RequestController->>RequestModel: originalRequest.save() (update responseCount)
    RequestModel-->>RequestController: updatedRequest
    RequestController-->>Client: 201 Created (newResponse)

    Client->>API Gateway/Router: POST /requests/:reqId/responses/:resId/accept (Accept Response)
    API Gateway/Router->>RequestController: acceptResponse(req, res)
    RequestController->>RequestService: acceptResponse(requestId, responseId, userId)
    RequestService-->>RequestController: updatedRequest
    RequestController-->>Client: 200 OK (updatedRequest)
```

## Explanation of the Flow

1.  **Creating a Request:**

    - A user (client) initiates a request to find a specific dish by sending a `POST` request to the `/requests/` endpoint.
    - The `RequestController` handles this, first creating an address record via the `AddressService`.
    - Then, it calls the `RequestService` to create the actual dish request, which is saved to the database via `RequestModel`.
    - The client receives the newly created request object as a response. (Previously, a WebSocket event `feed:newRequest` would have been broadcast to hosts).

2.  **Responding to a Request:**

    - A host (client) finds a request they can fulfill and submits a response by sending a `POST` request to the `/requests/:requestId/responses` endpoint.
    - The `RequestController` first validates the original request.
    - A new `Dish` document, specific to this response, is created and saved via `DishModel`.
    - A `Response` document linking the request, host, and new dish is created via `ResponseService` and saved via `ResponseModel`.
    - The original request's `responseCount` is updated.
    - The client (host) receives the new response object. (Previously, a WebSocket event `request:newResponse` would have been sent to the original requestor).

3.  **Accepting a Response:**
    - The user who created the request reviews the responses and accepts one by sending a `POST` request to the `/requests/:requestId/responses/:responseId/accept` endpoint.
    - The `RequestController` calls the `RequestService` to update the status of the request and the chosen response. This involves marking the request as "confirmed" and the response as "accepted_by_user".
    - The client (requestor) receives the updated request object, which now includes details of the accepted response. (Previously, WebSocket events `response:accepted` would have been sent to the chosen host, and `request:updated` to the requestor).

## Frontend Considerations (Post-WebSocket Removal)

With WebSocket events removed for these actions, the frontend will need to implement strategies like polling relevant endpoints to get updates:

- **Hosts:** May need to poll `/requests/feed` to see new requests.
- **Requestors:** May need to poll `/requests/:requestId/responses` to see new responses to their request, and poll `/requests/:requestId` to see if their request status has changed (e.g., to "confirmed" after accepting a response).
- **Hosts:** May need to poll `/requests/host-responses` or specific responses they've made to see if their status has changed (e.g., to "accepted_by_user").

This flow documentation, along with the [Request API Guide](request_api_guide.md), should provide a comprehensive overview for frontend integration.

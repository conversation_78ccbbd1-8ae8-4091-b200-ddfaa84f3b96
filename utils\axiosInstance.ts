import axios from "axios";
import { getToken } from "./localStorage";
import { create } from "domain";
import { strict } from "assert";
import { ca } from "date-fns/locale";

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BACKEND_API_URI,
  withCredentials: true,
});

axiosInstance.interceptors.request.use((config) => {
  const token = getToken();
  config.headers["Authorization"] = `Bearer ${token}`;

  return config;
});

export default axiosInstance;

export const endpoints = {
  auth: {
    me: "/user",
    signIn: "/auth/login",
    verifyEmail: "/auth/verify",
    setPasswordWithToken: "/auth/set-password",
    resendToken: `/auth/resend-verification`,
    forgetPassword: `/auth/forgot-password`,
  },
  userProfile: {
    create: "/userprofiles",
    get: "/userprofiles/me",
    updateProfile: "/userprofiles/me", // for address
    updateAddress: "/userprofiles/me/address",
  },
  host: {
    create: "/host",
    details: () => `/host/me`,
    hostInfo: (id: string | undefined) => `/host/${id}`,
    edit: (id: number) => `/host/me`,
    location: (id: string) => `/host/me/address`,
    removeDining: (hostId: string, locationId: string) =>
      `/host/${hostId}/dining/${locationId}`,
    dining: () => `/host/me/dining`,
    dish: `/host/me/dish/`,
    allHost: "/host",
  },
  address: {
    create: "/address/",
    edit: (id: string) => `/address/${id}`,
  },
  cuisines: {
    all: "/cuisine",
  },
  ingredients: {
    all: "/ingredient",
  },
  dish: {
    create: "/dish",
    delete: (id: string) => `/dish/${id}`,
    all: "/dish",
    edit: (id: string) => `/dish/${id}`,
    premade_dishes: "/dish?premade=true",
    published_dishes: "/dish?published=true",
    details: (id: string) => `/dish/${id}`,
  },
  cart: {
    get: "/cart",
    add: "/cart/items",
  },
  order: {
    create: "/order",
    get: "/order",
    details: (id: string) => `/order/${id}`,
    user: () => `/order/user/`,
    host: () => `/order/host/`,
    changeStatus: `/order/host-status`,
  },
  requests: {
    create: "/requests",
    feed: "/requests/feed",
    user: "/requests/user",
    hostResponses: "/requests/host-responses",
    respond: (requestId: string) => `/requests/${requestId}/responses`,
    responses: (requestId: string) => `/requests/${requestId}/responses`, // Added this line
    acceptResponse: (requestId: string, responseId: string) =>
      `/requests/${requestId}/responses/${responseId}/accept`,
    getById: (requestId: string) => `/requests/${requestId}`,
    update: (requestId: string) => `/requests/${requestId}`,
    delete: (requestId: string) => `/requests/${requestId}`,
  },
  stripe: {
    createPaymentIntent: "/stripe/create-payment-intent",
    createCheckoutSession: "/stripe/create-checkout-session",
    cancelSession: (sessionId: string) => `/stripe/session/${sessionId}/cancel`,
    session: "/stripe/session",
    verifyPayment: "/stripe/verify-payment",
    refund: "/stripe/refund",
    partialRefund: "/stripe/partial-refund",
    customerPaymentMethods: "/stripe/customer-payment-methods",
    savePaymentMethod: "/stripe/save-payment-method",
    setDefaultPaymentMethod: "/stripe/set-default-payment-method",
    deletePaymentMethod: "/stripe/delete-payment-method",
  },
  chat: {
    conversations: "/chat/conversations",
    conversationById: (id: string) => `/chat/conversations/${id}`,
    messages: (conversationId: string) =>
      `/chat/conversations/${conversationId}/messages`,
    unreadCount: "/chat/unread-count",
    upload: "/upload/chat",
  },
};

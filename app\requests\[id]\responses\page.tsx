"use client";

import React, { useState, useEffect, useCallback } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { getRequestById, getResponsesForRequest } from "@/api/requests";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { paths } from "@/routes/paths";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { ArrowLeft } from "lucide-react";
import { HeaderBase } from "@/components/layouts/core/header-base";
import { useBoolean } from "@/hooks/use-boolean";
import DetailedResponseCard from "@/components/request/DetailedResponseCard";
// Removed: import { ApiRequest, ApiResponse } from "@/types/request";

// --- START TYPE DEFINITIONS (should be centralized, copied for now) ---
// Ensure these types are active and not commented out if they were before.
interface ApiLocation {
  _id?: string;
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  coordinates?: [number, number];
  postalCode?: string;
  latitude?: number;
  longitude?: number;
}

interface ApiUser {
  _id: string;
  name?: string;
  avatar?: string;
}

interface ApiDishRequest {
  title: string;
  ingredients: string[];
  description?: string;
  offering: string;
  minServing: number;
  maxServing: number;
  quantity?: number;
  offeringTime?: string; // ISO Date string
}

interface ApiDishForResponse {
  _id: string;
  name: string;
  price: number;
  photos?: string[];
  description?: string;
}

interface ApiAcceptedResponseData {
  _id: string;
  host: ApiUser;
  dish: ApiDishForResponse;
  message: string;
  status: string;
}

// Re-defining ApiRequest and ApiResponse here if not globally available via @/types/request
// For the purpose of this file, ensure these match the structure used by DetailedResponseCard
// and the API.
export interface ApiRequest {
  _id: string;
  user: ApiUser;
  dishRequest: ApiDishRequest;
  status:
    | "pending_responses"
    | "confirmed"
    | "closed"
    | "assigned"
    | "active"
    | "open";
  location: ApiLocation;
  city?: string;
  responseCount?: number;
  expiresAt?: string | null;
  createdAt: string;
  updatedAt: string;
  acceptedResponseId?: string | ApiAcceptedResponseData;
  description?: string;
  offering?: string;
  offeringTime?: string;
}

export interface ApiResponse {
  _id: string;
  request: string; // Request ID
  host: ApiUser;
  dish?: ApiDishForResponse;
  message: string;
  status: "pending_user_action" | "accepted_by_user" | "declined_by_user";
  createdAt: string;
  price?: number;
  estimatedDeliveryTime?: string; // ISO Date string
}
// --- END TYPE DEFINITIONS ---

const POLLING_INTERVAL = 15000; // 15 seconds for polling responses
const RequestResponsesPage = () => {
  const params = useParams();
  const requestId = params.id as string;
  const router = useRouter();
  const { user: currentUser } = useAuthContext();
  const hamburgerMenu = useBoolean();

  const [request, setRequest] = useState<ApiRequest | null>(null);
  const [responses, setResponses] = useState<ApiResponse[]>([]);
  const [loadingRequest, setLoadingRequest] = useState(true);
  const [loadingResponses, setLoadingResponses] = useState(true);

  const fetchRequestDetails = useCallback(async () => {
    if (!requestId) return;
    setLoadingRequest(true);
    try {
      const data = await getRequestById(requestId);
      setRequest(data);
    } catch (error) {
      console.error("Error fetching request details:", error);
      toast.error("Failed to load request details.");
      // router.push(paths.page404); // Or some error page
    } finally {
      setLoadingRequest(false);
    }
  }, [requestId, router]);

  const fetchAllResponses = useCallback(async () => {
    if (!requestId) return;
    // Only set loading true on initial fetch, not for polling
    if (responses.length === 0) setLoadingResponses(true);
    try {
      const responseData = await getResponsesForRequest(requestId);
      setResponses(responseData.data || []);
    } catch (error) {
      console.error("Error fetching responses:", error);
      toast.error("Failed to load responses.");
    } finally {
      setLoadingResponses(false);
    }
  }, [requestId, responses.length]);

  useEffect(() => {
    fetchRequestDetails();
    fetchAllResponses(); // Initial fetch
  }, [fetchRequestDetails, fetchAllResponses]);

  // Polling for responses
  useEffect(() => {
    if (!requestId || !request) return; // Don't poll if no request ID or initial request not loaded

    // Only poll if the request is in a state where responses might change or matter
    const canPoll =
      request.status === "pending_responses" ||
      request.status === "open" ||
      request.status === "active" ||
      request.status === "confirmed";

    if (!canPoll) return;

    const intervalId = setInterval(fetchAllResponses, POLLING_INTERVAL);
    return () => clearInterval(intervalId);
  }, [requestId, request, fetchAllResponses]);

  const goBack = () => {
    router.push(`/requests/${requestId}`); // Corrected path
  };

  if (loadingRequest) {
    return (
      <>
        <HeaderBase hamburgerMenu={hamburgerMenu} />
        <div className="container mx-auto py-6 max-w-3xl mt-24">
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="sm" onClick={goBack} className="mr-2">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back
            </Button>
            <Skeleton className="h-8 w-1/2" />
          </div>
          <Skeleton className="h-12 w-3/4 mb-4" />
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="mb-4">
              <CardHeader>
                <Skeleton className="h-6 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </>
    );
  }

  if (!request) {
    return (
      <>
        <HeaderBase hamburgerMenu={hamburgerMenu} />
        <div className="container mx-auto py-6 max-w-3xl mt-24 text-center">
          <h2 className="text-xl font-semibold mb-4">Request Not Found</h2>
          <p className="text-gray-600 mb-4">
            The request you are looking for does not exist or could not be
            loaded.
          </p>
          <Button onClick={() => router.push(paths.root)}>Go to Feed</Button>
        </div>
      </>
    );
  }

  // Ensure current user is the requestor to view this page, or adjust logic as needed
  // For now, assuming only requestor sees this detailed response page.
  if (request.user._id !== currentUser?._id) {
    return (
      <>
        <HeaderBase hamburgerMenu={hamburgerMenu} />
        <div className="container mx-auto py-6 max-w-3xl mt-24 text-center">
          <h2 className="text-xl font-semibold mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">
            You do not have permission to view these responses.
          </p>
          <Button onClick={() => router.push(paths.root)}>Go to Feed</Button>
        </div>
      </>
    );
  }

  return (
    <>
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      <div className="container mx-auto py-6 max-w-3xl mt-24">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" onClick={goBack} className="mr-2">
            <ArrowLeft className="h-4 w-4 mr-1" /> Back to Request
          </Button>
          <h1 className="text-2xl font-bold">
            Responses for "{request.dishRequest.title}"
          </h1>
        </div>

        {loadingResponses && responses.length === 0 && (
          <div>
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="mb-4">
                <CardHeader>
                  <Skeleton className="h-6 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {!loadingResponses && responses.length === 0 && (
          <Card>
            <CardContent className="pt-6 text-center">
              <p className="text-gray-600 text-lg">
                No responses received yet.
              </p>
              {(request.status === "pending_responses" ||
                request.status === "open" ||
                request.status === "active") && (
                <p className="text-sm text-gray-500 mt-2">
                  Polling for new responses...
                </p>
              )}
            </CardContent>
          </Card>
        )}

        {!loadingResponses && responses.length > 0 && (
          <div className="space-y-6">
            {responses.map((response) => (
              <DetailedResponseCard
                key={response._id}
                response={response}
                request={request}
              />
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default RequestResponsesPage;

"use client";

import { useState } from "react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { MultiSelect } from "@/components/ui/multi-select";
import CompactLocationComponent from "@/components/map/CompactLocationComponent";
import { createRequest } from "@/api/requests";

// Updated form schema with address
const formSchema = z.object({
  title: z.string().min(2, {
    message: "Title must be at least 2 characters.",
  }),
  ingredients: z.array(z.string()).min(1, {
    message: "At least one ingredient must be selected.",
  }),
  description: z.string().optional(),
  offering: z.enum(["Dine In", "Take Away", "Delivery"]),
  minServing: z.number().min(1),
  maxServing: z.number().min(1),
  quantity: z.number().min(1),
  offeringTime: z.date().optional(),
  // New address schema
  address: z.object({
    street: z.string().min(1, { message: "Street is required" }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    country: z.string().min(1, { message: "Country is required" }),
    postalCode: z
      .string()
      .regex(/^\d{5}$/, { message: "Zip code must be 5 digits" }),
    latitude: z.number().optional(),
    longitude: z.number().optional(),
  }),
});

interface DishRequestFormProps {
  onSuccess?: (requestId?: string) => void;
  googleMapsApiKey?: string;
}

export default function DishRequestForm({
  onSuccess,
  googleMapsApiKey = "AIzaSyBSg-HHoQ4g04YLUbPUUrk_MNZsuB8wyPs", // Default API key, consider moving to env variables
}: DishRequestFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [ingredientOptions, setIngredientOptions] = useState([
    { label: "Tomato", value: "tomato" },
    { label: "Cheese", value: "cheese" },
    { label: "Chicken", value: "chicken" },
    { label: "Beef", value: "beef" },
    { label: "Lettuce", value: "lettuce" },
    { label: "Onion", value: "onion" },
    { label: "Garlic", value: "garlic" },
    { label: "Pasta", value: "pasta" },
    { label: "Rice", value: "rice" },
    { label: "Potato", value: "potato" },
  ]);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      ingredients: [],
      description: "",
      offering: "Dine In",
      minServing: 1,
      maxServing: 1,
      quantity: 1,
      offeringTime: new Date(),
      address: {
        street: "",
        city: "",
        state: "",
        country: "",
        postalCode: "",
        latitude: 39.7392,
        longitude: -104.9903,
      },
    },
  });

  const handleAddCustomIngredient = (customIngredient: string) => {
    const newIngredient = {
      label: customIngredient,
      value: customIngredient.toLowerCase(),
    };
    setIngredientOptions((prev) => [...prev, newIngredient]);
    form.setValue("ingredients", [
      ...form.getValues("ingredients"),
      newIngredient.value,
    ]);
  };

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    try {
      const { address, ...dishDetailsFromValues } = values;
      const payload = {
        ...dishDetailsFromValues, // Includes title, ingredients, description, offering, minServing, maxServing, quantity, offeringTime
        location: {
          street: address.street,
          city: address.city,
          state: address.state,
          country: address.country,
          postalCode: address.postalCode, // Mapped from form's postalCode to API's zipCode
          longitude: address.longitude,
          latitude: address.latitude,
        },
      };
      const response = await createRequest(payload);
      setIsSubmitting(false); // Set submitting to false after API call

      if (response && response._id) {
        form.reset();
        toast.success("Request submitted successfully!");
        onSuccess?.(response._id); // Pass the new request ID to the callback
      } else {
        // This case implies the API call was 'successful' (no throw) but didn't return an _id.
        // This shouldn't happen if the API conforms to the documentation.
        console.error(
          "Request creation succeeded but no ID was returned.",
          response
        );
        toast.error(
          "Request created, but an issue occurred retrieving its details. Please check your requests."
        );
        onSuccess?.(); // Call onSuccess without ID as a fallback if needed
      }
    } catch (error) {
      setIsSubmitting(false);
      console.error("Submission error:", error);
      // Attempt to get a more specific error message if available
      const errorMessage =
        (error as any)?.response?.data?.message ||
        (error as Error)?.message ||
        "Failed to submit request. Please try again.";
      toast.error(errorMessage);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Enter dish title" {...field} />
              </FormControl>
              <FormDescription>
                The name of the dish you&apos;re requesting.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="ingredients"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Ingredients</FormLabel>
              <FormControl>
                <MultiSelect
                  options={ingredientOptions}
                  selected={field.value}
                  onChange={field.onChange}
                  onAddCustom={handleAddCustomIngredient}
                  placeholder="Select or add ingredients"
                />
              </FormControl>
              <FormDescription>
                Select or add custom ingredients for your dish.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the dish (optional)"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Location Section */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium mb-3">Location</h3>
          <CompactLocationComponent
            form={form}
            googleMapsApiKey={googleMapsApiKey}
          />
        </div>

        <FormField
          control={form.control}
          name="offering"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Offering Type</FormLabel>
              <Select
                onValueChange={(value) => {
                  field.onChange(value);
                  if (value === "Delivery") {
                    form.setValue("offeringTime", undefined);
                  } else if (!form.getValues("offeringTime")) {
                    form.setValue("offeringTime", new Date());
                  }
                }}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select offering type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Dine In">Dine In</SelectItem>
                  <SelectItem value="Take Away">Take Away</SelectItem>
                  <SelectItem value="Delivery">Delivery</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex flex-wrap gap-4">
          <FormField
            control={form.control}
            name="minServing"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Min Serving</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) => field.onChange(+e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="maxServing"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Max Serving</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) => field.onChange(+e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="quantity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Quantity</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    {...field}
                    onChange={(e) => field.onChange(+e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {form.watch("offering") !== "Delivery" && (
          <FormField
            control={form.control}
            name="offeringTime"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>
                  {form.watch("offering") === "Dine In"
                    ? "Dine In Time"
                    : "Pickup Time"}
                </FormLabel>
                <DateTimePicker date={field.value} setDate={field.onChange} />
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <Button type="submit" disabled={isSubmitting} className="w-full">
          {isSubmitting ? "Submitting..." : "Submit Request"}
        </Button>
      </form>
    </Form>
  );
}

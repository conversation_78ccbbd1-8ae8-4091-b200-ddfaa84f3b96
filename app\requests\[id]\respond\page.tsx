"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { getDishDetails } from "@/api/host";
import { getRequestById, respondToRequest } from "@/api/requests";
import { Dish } from "@/types/host";
import DishCard from "@/components/cards/DishCard";
import { Spinner } from "@/components/ui/spinner";
import { Skeleton } from "@/components/ui/skeleton";

// Type definitions based on request_response_doc.md #4
interface RequestUser {
  _id: string;
  name: string;
  // Add other relevant user fields if needed
}

interface RequestLocation {
  _id: string;
  city: string;
  street: string;
  // Add other relevant location fields if needed (e.g., state, zipCode, country)
}

interface DishRequestDetails {
  title: string;
  ingredients?: string[];
  description?: string;
  offering: string;
  minServing?: number;
  maxServing?: number;
  quantity: number;
  offeringTime: string; // ISO date string
}

interface RequestData {
  _id: string;
  user: RequestUser;
  dishRequest: DishRequestDetails;
  status: string;
  location: RequestLocation;
  city: string; // As per API doc, might be redundant with location.city
  responseCount: number;
  expiresAt: string | null;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

// Validation schema for response form
const responseSchema = z.object({
  message: z.string().min(10, "Message must be at least 10 characters"),
});

type ResponseFormData = z.infer<typeof responseSchema>;

export default function RespondPage() {
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();
  const requestId = params.id as string;
  const dishId = searchParams.get("dishId");

  const [dish, setDish] = useState<Dish | null>(null);
  const [request, setRequest] = useState<RequestData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ResponseFormData>({
    resolver: zodResolver(responseSchema),
    defaultValues: {
      message: "",
    },
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [dishResponse, requestResponse] = await Promise.all([
          getDishDetails(dishId as string),
          getRequestById(requestId),
        ]);

        setDish(dishResponse.data);
        setRequest(requestResponse.data);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to load required data");
      } finally {
        setIsLoading(false);
      }
    };

    if (dishId && requestId) {
      fetchData();
    }
  }, [dishId, requestId, form]);

  const onSubmit = async (data: ResponseFormData) => {
    if (!dish) return;

    setIsSubmitting(true);
    try {
      await respondToRequest(requestId, {
        ...data,
        dishId: dish._id,
      });

      toast.success("Response submitted successfully");
      router.push("/requests/responses");
    } catch (error) {
      console.error("Error submitting response:", error);
      toast.error("Failed to submit response");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 max-w-4xl">
        {/* Back button skeleton */}
        <Skeleton className="h-10 w-20 mb-6" />

        <div className="grid gap-6 md:grid-cols-2">
          {/* Dish card skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-64" />
            </CardHeader>
            <CardContent>
              <div className="w-80 border rounded-lg overflow-hidden">
                <Skeleton className="w-full h-48" />
                <div className="p-4 space-y-2">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-4 w-2/3" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Response form skeleton */}
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-40" />
              <Skeleton className="h-4 w-56" />
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-32 w-full" /> {/* Adjusted height */}
                  <Skeleton className="h-3 w-64" />
                </div>
                <Skeleton className="h-10 w-full" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Loading indicator */}
        <div className="flex justify-center items-center mt-8">
          <Spinner size="md" className="text-primary" />
          <span className="ml-2 text-gray-600">Loading dish details...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <Button variant="ghost" className="mb-6" onClick={() => router.back()}>
        <ArrowLeft className="h-4 w-4 mr-2" /> Back
      </Button>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Respond with Selected Dish</CardTitle>
            <CardDescription>
              Review the dish details and submit your response
            </CardDescription>
          </CardHeader>
          <CardContent>
            {dish && <DishCard dish={dish} hideActions />}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Response Details</CardTitle>
            <CardDescription>
              Provide additional details for your response
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="message"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Message to Customer</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter your message to the customer"
                          {...field}
                          rows={5}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide any additional information about your dish or
                        service
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit Response"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

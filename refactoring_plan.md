# Refactoring Plan: Request-Response Flow and UI/UX Enhancements

**Overall Goals:**

1.  **Remove WebSocket Functionality:** Eliminate all WebSocket-related code from the specified pages.
2.  **Implement Polling:** Introduce polling mechanisms (15-second interval) to fetch updates as per `request_response_flow.md`.
3.  **Align with API Specification:** Ensure all API interactions strictly follow `request_response_doc.md`.
4.  **Enhance UI/UX:** Improve the user interface and experience using the current design system and modern best practices. This includes:
    - Clearer information hierarchy.
    - Intuitive navigation and user flows.
    - Consistent visual language.
    - Responsive design considerations.
    - Appropriate loading states and feedback.
5.  **Role-Specific Views:** Adapt UI and functionality based on user roles (requestor vs. host) where applicable.

**Mermaid Diagram of Proposed Frontend Flow (Simplified):**

```mermaid
sequenceDiagram
    participant User
    participant PageComponent
    participant PollingService
    participant APIClient
    participant BackendAPI

    User->>PageComponent: Interacts with UI (e.g., views feed, opens request)
    PageComponent->>PollingService: Start polling for relevant data (e.g., /requests/feed)
    loop Every 15 seconds
        PollingService->>APIClient: Fetch data (e.g., getRequestFeed())
        APIClient->>BackendAPI: HTTP GET /requests/feed
        BackendAPI-->>APIClient: API Response
        APIClient-->>PollingService: Parsed Data
        PollingService->>PageComponent: Update state with new data
        PageComponent->>User: Seamlessly refresh UI
    end

    User->>PageComponent: Performs action (e.g., create request, submit response)
    PageComponent->>APIClient: Perform action (e.g., createRequest(payload))
    APIClient->>BackendAPI: HTTP POST /requests
    BackendAPI-->>APIClient: API Response (e.g., newRequest object)
    APIClient-->>PageComponent: Action Result
    PageComponent->>User: Update UI, show confirmation/error
    alt Next Polling Cycle
        PollingService->>APIClient: Fetch data
        APIClient->>BackendAPI: HTTP GET /requests/feed
        BackendAPI-->>APIClient: API Response (reflecting the action)
        APIClient-->>PollingService: Parsed Data
        PollingService->>PageComponent: Update state
        PageComponent->>User: UI reflects latest state
    end
```

**Detailed Plan for Each File:**

---

### 1. `app/requests/page.tsx` (Host Request Feed)

- **Current State:** Displays a feed of requests for hosts, uses WebSockets for real-time updates, includes filtering.
- **Refactoring Steps:**
  1.  **Remove WebSocket:**
      - Remove `useSocket` hook and related `useEffect`.
      - Delete event listeners for `feed:newRequest` and `request:accepted`.
  2.  **Implement Polling for Request Feed:**
      - Create a custom hook or integrate logic to call `getRequestFeed()` every 15 seconds.
      - Update `requests` and `totalPages` state seamlessly.
  3.  **API Alignment:**
      - Verify `getRequestFeed()` parameters and response handling align with `request_response_doc.md#2-get-request-feed`.
  4.  **UI/UX Enhancements:**
      - Improve loading states.
      - Ensure filter controls are intuitive.
      - Review `renderRequestItem` for clarity and visual appeal.
      - Improve empty states.

---

### 2. `app/requests/responses/page.tsx` (Host's Own Responses)

- **Current State:** Displays responses by the host, uses WebSockets for `request:accepted`.
- **Refactoring Steps:**
  1.  **Remove WebSocket:**
      - Remove `useSocket` hook and `useEffect`.
  2.  **Implement Polling for Host Responses:**
      - Poll `getHostResponses()` every 15 seconds, aligning with `request_response_doc.md#5-get-hosts-responses`.
  3.  **API Alignment:**
      - Confirm `getHostResponses` and `Response` interface align with API.
  4.  **UI/UX Enhancements:**
      - Review status display (`getStatusColor`, `getStatusIcon`).
      - Improve `renderResponseItem` card design.
      - Ensure tab filtering aligns with backend statuses.

---

### 3. `app/requests/request-form/page.tsx` (User Creates a Request)

- **Current State:** Form for users to create requests using `DishRequestForm`.
- **Refactoring Steps:**
  1.  **WebSocket Removal:** Check `DishRequestForm` for any WebSocket usage.
  2.  **API Alignment (within `DishRequestForm`):**
      - Ensure submission `POSTS` to `/requests/` per `request_response_doc.md#1-create-request`.
      - `onSuccess` should navigate user to view their new request.
  3.  **UI/UX Enhancements:**
      - Review `DishRequestForm` (likely `components/request/requestForm.tsx`) for clarity, validation, input types, and feedback (use toasts instead of `alert`).

---

### 4. `app/requests/[id]/page.tsx` (View Single Request Details)

- **Current State:** Displays request details, allows host responses, uses WebSockets for `request:accepted`.
- **Refactoring Steps:**
  1.  **Remove WebSocket:**
      - Remove `useSocket` hook and `useEffect`.
  2.  **Implement Polling (Conditional):**
      - **Requestor:** Poll `getRequestById(requestId)` and `/requests/:requestId/responses`.
      - **Host:** Polling `getRequestById(requestId)` may be less critical.
  3.  **API Alignment:**
      - `getRequestById` aligns with `request_response_doc.md#4-get-request-by-id`.
      - Response submission (`onSubmit`, `RespondWithDishButton`) aligns with `POST /requests/:requestId/responses`. Clarify handling of new vs. existing dish responses.
  4.  **UI/UX Enhancements & Role-Specific Views:**
      - **Requestor View:** Display request, list responses, "Accept Response" button calling `POST /requests/:reqId/responses/:resId/accept`.
      - **Host View:** Prominent "Respond to Request" section if request is open. Disable if closed.
      - Polish loading and not found states.

---

### 5. `app/requests/[id]/respond/page.tsx` (Host Responds with a Specific Dish)

- **Current State:** Allows host to respond with an existing dish.
- **Refactoring Steps:**
  1.  **WebSocket Removal:** No direct WebSocket usage apparent.
  2.  **API Alignment:**
      - `onSubmit` calls `respondToRequest`. Payload is for existing dish.
      - Ensure consistency for `estimatedDeliveryTime` with API.
  3.  **UI/UX Enhancements:**
      - Clear `DishCard` display.
      - Clear form fields.
      - Polish loading/submitting states.

---

**General Considerations:**

- **Error Handling:** Consistent, user-friendly error messages (e.g., `sonner` toasts).
- **State Management:** Efficient state management (React `useState`, context).
- **Custom Hooks:** Use for polling logic.
- **Types/Interfaces:** Accurate types based on `request_response_doc.md`.
- **Code Consistency & Component Reusability.**

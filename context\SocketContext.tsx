"use client";

import React, {
  createContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from "react";
import { io, Socket } from "socket.io-client";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { getToken } from "@/utils/localStorage";

// Context interface
interface SocketContextProps {
  socket: Socket | null;
  isConnected: boolean;
  connect: () => void;
  disconnect: () => void;
}

// Create the context with default values
const SocketContext = createContext<SocketContextProps>({
  socket: null,
  isConnected: false,
  connect: () => {},
  disconnect: () => {},
});

// Socket provider props
interface SocketProviderProps {
  children: ReactNode;
}

/**
 * Socket Provider component to manage WebSocket connections
 */
export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const tokenRef = React.useRef<string | null>(null);
  const connectingRef = React.useRef<boolean>(false);
  const { user, host, authenticated } = useAuthContext();

  // Get token once on mount and when authentication changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      tokenRef.current = getToken();
    }
  }, [authenticated]);
  // Connect to socket with authentication
  const connect = useCallback(() => {
    // Only attempt to connect if we're in the browser and have a token
    if (!tokenRef.current || typeof window === "undefined") {
      console.log("Cannot connect: No token or not in browser");
      return;
    }

    // Prevent multiple simultaneous connection attempts
    if (connectingRef.current) {
      console.log("Connection already in progress, skipping");
      return;
    }

    // CRITICAL FIX: If we already have a connected socket, don't create a new one
    if (socket && isConnected) {
      console.log(
        "🔌 [SOCKET DEBUG] Socket already connected, not creating a new one",
        {
          socketId: socket.id,
          userId: user?._id,
          hostId: host?._id,
        }
      );
      return;
    }

    // If we have a socket but it's not connected, try to reconnect
    if (socket) {
      if (!isConnected) {
        console.log("Reconnecting existing socket...");

        // Clean up existing listeners before reconnecting
        socket.off();

        // Try to reconnect
        socket.connect();
      }
      return;
    }

    // If we get here, we need to create a new socket
    connectingRef.current = true;

    // Create socket connection with authentication token
    console.log("🔌 [SOCKET DEBUG] Creating new socket connection...", {
      existingSocket: !!socket,
      isConnected: isConnected,
      token: !!tokenRef.current,
      timestamp: new Date().toISOString(),
    });

    // Define the socket options
    const socketOptions = {
      auth: {
        token: tokenRef.current,
      },
      path: "/socket.io", // As per WEBSCOKET_CLIENT_GUIDE.md (line 43)
      transports: ["websocket", "polling"], // Allow both for better compatibility
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000, // Increase timeout
      autoConnect: false, // Don't connect automatically
    };

    console.log("Socket options:", socketOptions);

    const socketInstance = io(
      process.env.NEXT_PUBLIC_API_URL || "http://localhost:5000",
      socketOptions
    );

    // Add logging for ALL socket events (including system events)
    const eventsToLog = [
      "connect",
      "disconnect",
      "connect_error",
      "reconnect",
      "reconnect_attempt",
      "reconnect_error",
      "reconnect_failed",
      "error",
      "connection:success",
      "chat:message_received",
      "chat:message_sent",
      "chat:user_typing",
      "chat:message_read",
      "chat:messages_read",
      "chat:conversation_created",
      "chat:join_conversation",
      "chat:leave_conversation",
      "chat:send_message",
      "chat:typing",
      "chat:read_messages",
      "join:user",
      "join:host",
    ];

    // Register only non-chat debugging events to avoid conflicts with ChatWindow
    const debugEventsToLog = [
      "connect",
      "disconnect",
      "connect_error",
      "reconnect",
      "reconnect_attempt",
      "reconnect_error",
      "reconnect_failed",
      "error",
      "connection:success",
      "join:user",
      "join:host",
    ];

    debugEventsToLog.forEach((eventName) => {
      socketInstance.on(eventName, (...args) => {
        console.group(`🎯 [SOCKET EVENT] ${eventName}`);
        console.log("Timestamp:", new Date().toISOString());
        console.log("Socket ID:", socketInstance.id || "Not connected");
        console.log("Event Args:", args);
        console.groupEnd();
      });
    });

    // Catch-all event listener for debugging unknown events
    // Use onAny for socket.io-client v3/v4
    socketInstance.onAny((eventName, ...args) => {
      console.group(`🔍 [SOCKET ALL EVENTS] ${eventName}`);
      console.log("Timestamp:", new Date().toISOString());
      console.log("Socket ID:", socketInstance.id || "Not connected");
      console.log("Event Name:", eventName);
      console.log("Args:", args);
      console.groupEnd();
    });

    // Remove duplicate chat event listeners - these are handled in ChatWindow
    // Only keep essential connection debugging

    // Event listeners for connection status
    socketInstance.on("connect", () => {
      console.log("🔌 [SOCKET DEBUG] Socket connected successfully", {
        socketId: socketInstance.id,
        userId: user?._id,
        hostId: host?._id,
        timestamp: new Date().toISOString(),
      });
      setIsConnected(true);
      connectingRef.current = false;

      // Join appropriate rooms based on user type
      if (user?._id) {
        console.log(`🏠 [SOCKET DEBUG] Joining user room for user ${user._id}`);
        socketInstance.emit("join:user", { userId: user._id });
      } else {
        console.log(
          "⚠️ [SOCKET DEBUG] User ID not available, skipping user room join"
        );
      }

      if (host?._id) {
        console.log(`🏠 [SOCKET DEBUG] Joining host room for host ${host._id}`);
        socketInstance.emit("join:host", { hostId: host._id });
      } else {
        console.log(
          "⚠️ [SOCKET DEBUG] Host ID not available, skipping host room join"
        );
      }
    });

    socketInstance.on("connection:success", (data) => {
      console.log("Received connection success event:", data);
    });

    socketInstance.on("connect_error", (error) => {
      // Log the error message if available, otherwise the full error object
      const errorMessage =
        error && error.message ? error.message : "Unknown connection error";
      console.error("Socket connection error:", errorMessage, error);
      setIsConnected(false);
      connectingRef.current = false;

      // Detailed error handling based on WEBSCOKET_CLIENT_GUIDE.md (lines 115-121, 125-142)
      if (error && error.message) {
        const specificMessage = error.message;
        if (specificMessage === "AUTH_TOKEN_EXPIRED") {
          console.warn(
            "Socket Auth: Token expired. User may need to re-authenticate or token needs refresh."
          );
          // Application should handle token refresh and reconnect, or prompt user.
          // e.g., socketInstance.disconnect(); // if immediate disconnect is desired
        } else if (
          [
            "AUTH_TOKEN_REQUIRED",
            "AUTH_TOKEN_INVALID",
            "AUTH_FAILED_INVALID_TOKEN",
          ].includes(specificMessage)
        ) {
          console.warn(
            `Socket Auth Error: ${specificMessage}. User should re-login.`
          );
          // Application should handle redirecting to login or prompting user.
          // e.g., socketInstance.disconnect(); // if immediate disconnect is desired
        } else if (specificMessage === "AUTH_MIDDLEWARE_ERROR") {
          console.error(
            "Socket Auth: Critical error in server-side authentication middleware."
          );
        }
        // Add other specific error messages from the guide if necessary
      }
    });

    socketInstance.on("disconnect", (reason) => {
      console.log("Socket disconnected:", reason);
      setIsConnected(false);

      // Don't automatically reconnect on certain disconnect reasons
      if (
        reason === "io server disconnect" ||
        reason === "io client disconnect"
      ) {
        console.log("Not attempting to reconnect due to explicit disconnect");
      }
    });

    socketInstance.on("error", (serverError) => {
      // Handle structured server errors as per WEBSCOKET_CLIENT_GUIDE.md (lines 173-180, 215-225)
      if (
        serverError &&
        typeof serverError === "object" &&
        "code" in serverError &&
        "message" in serverError
      ) {
        console.error(
          "Server emitted an application error:",
          `Code: ${serverError.code},`,
          `Message: "${serverError.message}"`,
          serverError.details !== undefined // Check if details exists
            ? `Details: ${JSON.stringify(serverError.details)}`
            : "(No additional details provided)"
        );
      } else {
        // Fallback for undifferentiated or non-standard errors
        console.error(
          "Socket received an 'error' event with unexpected format:",
          serverError
        );
      }
    });

    // Manually connect after setting up all event handlers
    console.log("Manually connecting socket...");
    socketInstance.connect();

    // Store cleanup function for debug listeners
    const cleanupDebugListeners = () => {
      console.log("🧹 [SOCKET] Cleaning up debug event listeners");
      debugEventsToLog.forEach((eventName) => {
        socketInstance.removeAllListeners(eventName);
      });
    };

    // Store cleanup function on socket instance for later use
    (socketInstance as any)._cleanupDebugListeners = cleanupDebugListeners;

    setSocket(socketInstance);
  }, [user?.id, host?._id]); // Only depend on user/host IDs to prevent loops

  // Disconnect from socket
  const disconnect = useCallback(() => {
    if (socket) {
      console.log("🔌 [SOCKET DEBUG] Disconnecting socket:", socket.id);

      // CRITICAL FIX: Clean up all listeners before disconnecting
      if ((socket as any)._cleanupDebugListeners) {
        (socket as any)._cleanupDebugListeners();
      }

      // Remove all listeners to prevent memory leaks
      socket.removeAllListeners();
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
      connectingRef.current = false;

      console.log("✅ [SOCKET DEBUG] Socket disconnected and cleaned up");
    }
  }, [socket]);

  // Effect to handle auto-connection when authenticated and token is available
  useEffect(() => {
    // Only run this effect in the browser
    if (typeof window === "undefined") return;

    // Only connect if we have a token and are authenticated
    if (
      tokenRef.current &&
      authenticated &&
      !isConnected &&
      !connectingRef.current
    ) {
      console.log("Attempting to connect socket...");

      // Add a small delay before connecting to ensure everything is initialized
      const connectTimer = setTimeout(() => {
        connect();
      }, 500);

      return () => {
        clearTimeout(connectTimer);
      };
    } else if (!tokenRef.current || !authenticated) {
      // Disconnect if no token or not authenticated
      disconnect();
    }
  }, [authenticated, isConnected, connect, disconnect]); // Include connect and disconnect but they're stable

  // Cleanup effect on unmount
  useEffect(() => {
    return () => {
      if (socket) {
        console.log("Cleaning up socket on unmount");
        socket.disconnect();
        setSocket(null);
        setIsConnected(false);
      }
    };
  }, []); // Empty dependency array - only run on mount/unmount

  // Memoize context value to prevent unnecessary re-renders
  const value = React.useMemo(
    () => ({
      socket,
      isConnected,
      connect,
      disconnect,
    }),
    [socket, isConnected, connect, disconnect]
  );

  return (
    <SocketContext.Provider value={value}>{children}</SocketContext.Provider>
  );
};

export default SocketContext;

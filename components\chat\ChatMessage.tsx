"use client";

import React from "react";
import Image from "next/image";
import { format } from "date-fns";
import styles from "@/styles/Chat.module.css";

interface Attachment {
  url: string;
  type: string;
  name: string;
}
interface Profile {
  _id: string;
  name?: string;
  avatar?: string;
  title?: string;
}
interface ReadByEntry {
  user: string;
  readAt: string;
  _id: string;
}

interface Message {
  _id: string;
  conversationId: string;
  content: string;
  sender: Profile;
  senderType: "user" | "host" | "system";
  createdAt: string;
  updatedAt: string;
  readBy: ReadByEntry[];
  attachments?: Attachment[];
  isSystem?: boolean;
  __v?: number;
}

interface ChatMessageProps {
  message: Message;
  isOwnMessage: boolean;
  otherParticipant: any;
  currentUser: any;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isOwnMessage,
  otherParticipant,
  currentUser,
}) => {
  // No temporary message logic needed anymore
  // Format time for display
  // console.log("Rendering ChatMessage component", {
  //   message,
  //   isOwnMessage,
  //   otherParticipant,
  //   currentUser,
  // });
  const formatMessageTime = (dateString: string) => {
    // Debug logs for diagnosis
    //  console.log("[ChatMessage] formatMessageTime input:", dateString);
    const dateObj = new Date(dateString);
    // console.log(
    //   "[ChatMessage] Parsed date object:",
    //   dateObj,
    //   "isValid:",
    //   !isNaN(dateObj.getTime())
    // );
    return format(dateObj, "h:mm a");
  };

  // Check if message is read by the other participant
  const getReadStatus = () => {
    if (!message.readBy || message.readBy.length === 0) {
      return { isRead: false, readAt: null };
    }

    if (isOwnMessage) {
      // For own messages, check if the other participant has read it
      const otherParticipantId = otherParticipant?._id || "";
      const readEntry = message.readBy.find(
        (entry) => entry.user === otherParticipantId
      );
      return {
        isRead: !!readEntry,
        readAt: readEntry?.readAt || null,
      };
    } else {
      // For other's messages, check if current user has read it
      const currentUserId = currentUser?._id || currentUser?.hostId || "";
      const readEntry = message.readBy.find(
        (entry) => entry.user === currentUserId
      );
      return {
        isRead: !!readEntry,
        readAt: readEntry?.readAt || null,
      };
    }
  };

  const { isRead, readAt } = getReadStatus();

  // Create read status display component
  const ReadStatusIcon = () => {
    if (!isOwnMessage) return null;

    // Format read time for tooltip
    const formatReadTime = (readTimestamp: string) => {
      try {
        const readDate = new Date(readTimestamp);
        const now = new Date();
        const diffInMinutes = Math.floor(
          (now.getTime() - readDate.getTime()) / (1000 * 60)
        );

        if (diffInMinutes < 1) {
          return "Just now";
        } else if (diffInMinutes < 60) {
          return `${diffInMinutes} minute${diffInMinutes > 1 ? "s" : ""} ago`;
        } else if (diffInMinutes < 1440) {
          // Less than 24 hours
          const hours = Math.floor(diffInMinutes / 60);
          return `${hours} hour${hours > 1 ? "s" : ""} ago`;
        } else {
          return format(readDate, "MMM d, h:mm a");
        }
      } catch (error) {
        return "Read";
      }
    };

    if (isRead && readAt) {
      const readTimeFormatted = formatReadTime(readAt);
      return (
        <span
          className={`${styles.readStatus} ${styles.read}`}
          title={`Read ${readTimeFormatted}`}
        >
          ✓✓
        </span>
      );
    } else if (isRead) {
      return (
        <span className={`${styles.readStatus} ${styles.read}`} title="Read">
          ✓✓
        </span>
      );
    } else {
      return (
        <span className={styles.readStatus} title="Sent">
          ✓
        </span>
      );
    }
  };

  // Render system message
  if (message.senderType === "system") {
    return (
      <div className={styles.systemMessage}>
        <p>{message.content}</p>
        <span className={styles.messageTime}>
          {formatMessageTime(message.createdAt)}
        </span>
      </div>
    );
  }

  // Render attachments
  const renderAttachments = () => {
    if (!message.attachments || message.attachments.length === 0) return null;

    return (
      <div className={styles.attachments}>
        {message.attachments.map((attachment, index) => {
          // Handle images
          if (attachment.type.startsWith("image/")) {
            return (
              <div key={index} className={styles.imageAttachment}>
                <Image
                  src={attachment.url}
                  alt={attachment.name}
                  width={200}
                  height={150}
                  className={styles.attachmentImage}
                />
              </div>
            );
          }

          // Handle other file types
          return (
            <a
              key={index}
              href={attachment.url}
              target="_blank"
              rel="noopener noreferrer"
              className={styles.fileAttachment}
            >
              <div className={styles.fileIcon}>📎</div>
              <div className={styles.fileName}>{attachment.name}</div>
            </a>
          );
        })}
      </div>
    );
  };

  return (
    <div
      className={`
      ${styles.messageContainer} 
      ${isOwnMessage ? styles.ownMessage : styles.otherMessage}
    `}
    >
      {!isOwnMessage && (
        <div className={styles.messageAvatar}>
          {otherParticipant?.avatar ? (
            <Image
              src={otherParticipant.avatar}
              alt={otherParticipant.name || otherParticipant.title || "User"}
              width={32}
              height={32}
              className={styles.avatarImage}
            />
          ) : (
            <div className={styles.defaultAvatar}>
              {(
                otherParticipant?.name ||
                otherParticipant?.title ||
                "U"
              ).charAt(0)}
            </div>
          )}
        </div>
      )}

      <div className={styles.messageContent}>
        <div className={styles.messageBubble}>
          {message.content}
          {renderAttachments()}
        </div>

        <div className={styles.messageInfo}>
          <span className={styles.messageTime}>
            {formatMessageTime(message.createdAt)}
          </span>

          <ReadStatusIcon />
        </div>
      </div>
    </div>
  );
};

export default ChatMessage;

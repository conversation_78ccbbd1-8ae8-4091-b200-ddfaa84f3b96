"use client";

import React, { useState, useEffect, use<PERSON>allback } from "react";
import { useRouter } from "next/navigation";
import { getRequestFeed } from "@/api/requests";
import { useAuthContext } from "@/context/hooks/use-auth-hook";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow, format } from "date-fns";
import { toast } from "sonner";
import {
  MapPin,
  Calendar,
  Users,
  ChevronRight,
  Filter,
  UserCircle2,
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { paths } from "@/routes/paths";
import { HeaderBase } from "@/components/layouts/core/header-base";
import { useBoolean } from "@/hooks/use-boolean";

// Types for Request Feed aligned with request_response_doc.md#2-get-request-feed
interface FeedDishRequest {
  title: string;
  offering: string;
  quantity: number;
  offeringTime?: string;
}

interface FeedLocation {
  _id: string;
  street: string;
  city: string;
}

interface Request {
  _id: string;
  dishRequest: FeedDishRequest;
  location: FeedLocation;
  user: {
    _id: string;
    name: string;
  };
  status: string;
  createdAt: string;
  city: string; // Top-level city from API doc
  responseCount: number; // From API doc
}

// Custom Hook for fetching and polling request feed
const useRequestFeed = (
  page: number,
  setRequests: React.Dispatch<React.SetStateAction<Request[]>>,
  setTotalPages: React.Dispatch<React.SetStateAction<number>>,
  setPageLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const fetchFeedData = useCallback(
    async (isInitialLoad: boolean) => {
      if (isInitialLoad) {
        setPageLoading(true);
      }
      // No separate internalLoading state for silent poll, main loading state is not set for polls.

      try {
        const params: any = { page, limit: 10 }; // API doc default for limit is 20, current code uses 10. Retaining 10.
        // Filters for 'offering' and 'distance' are removed from API call
        // as they are not specified in request_response_doc.md#2-get-request-feed

        const response = await getRequestFeed(params);
        setRequests(response.requests);
        setTotalPages(response.totalPages);
      } catch (error) {
        console.error("Error fetching request feed:", error);
        toast.error(
          "Failed to load request feed. Please check your connection or try again later."
        );
        // Optionally, clear requests or show a specific error state in UI
        // setRequests([]);
        // setTotalPages(0);
      } finally {
        if (isInitialLoad) {
          setPageLoading(false);
        }
      }
    },
    [page, setRequests, setTotalPages, setPageLoading]
  );

  // Effect for initial load and page changes
  useEffect(() => {
    fetchFeedData(true);
  }, [fetchFeedData, page]); // fetchFeedData dependency ensures it's recreated if its own dependencies change

  // Effect for polling
  useEffect(() => {
    const intervalId = setInterval(() => {
      fetchFeedData(false); // Silent fetch, does not setPageLoading(true)
    }, 15000); // Poll every 15 seconds

    return () => clearInterval(intervalId); // Cleanup interval
  }, [fetchFeedData]); // Re-run if fetchFeedData function instance changes
};

const HostRequestFeed = () => {
  const router = useRouter();
  const { host } = useAuthContext(); // Assuming host context might be used for other purposes
  const [loading, setLoading] = useState(true);
  const [requests, setRequests] = useState<Request[]>([]);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filter, setFilter] = useState({
    // Filter state remains for UI controls
    offering: "", // Not used in API call to getRequestFeed as per docs
    distance: "", // Not used in API call to getRequestFeed as per docs
  });

  // Use the custom hook for data fetching and polling
  useRequestFeed(page, setRequests, setTotalPages, setLoading);

  const handleViewRequest = (requestId: string) => {
    router.push(`${paths.host.requestDetails}/${requestId}`);
  };

  const renderRequestItem = (request: Request) => {
    return (
      <Card
        key={request._id}
        className="mb-4 hover:shadow-md transition-shadow"
      >
        <CardHeader className="pb-3">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-xl mb-1">
                {request.dishRequest.title}
              </CardTitle>
              <div className="flex items-center text-sm text-muted-foreground mb-1">
                <MapPin className="h-4 w-4 mr-1.5" />
                {request.location.street}, {request.location.city}
              </div>
              <div className="flex items-center text-xs text-muted-foreground">
                <UserCircle2 className="h-3.5 w-3.5 mr-1.5" />
                Requested by {request.user.name}
              </div>
            </div>
            <Badge
              variant={
                request.dishRequest.offering === "Delivery"
                  ? "destructive"
                  : request.dishRequest.offering === "Take Away"
                  ? "default"
                  : "outline"
              }
              className="capitalize"
            >
              {request.dishRequest.offering}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pb-3">
          {/* Description removed as it's not in FeedDishRequest type */}
          {/* Ingredients removed as they are not in FeedDishRequest type */}

          <div className="grid grid-cols-1 gap-2 text-sm">
            {" "}
            {/* Changed to grid-cols-1 as servings info is removed */}
            {/* Servings info (minServing, maxServing) removed as it's not in FeedDishRequest type */}
            {request.dishRequest.offeringTime && (
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                <span>
                  {format(
                    new Date(request.dishRequest.offeringTime),
                    "MMM d, yyyy h:mm a"
                  )}
                </span>
              </div>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-between items-center pt-3 border-t">
          <div className="text-sm text-gray-500">
            Posted{" "}
            {formatDistanceToNow(new Date(request.createdAt), {
              addSuffix: true,
            })}
          </div>
          <Button
            variant="ghost"
            size="sm"
            className="flex items-center"
            onClick={() => handleViewRequest(request._id)}
          >
            View details <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </CardFooter>
      </Card>
    );
  };

  const renderSkeleton = () => (
    <div className="space-y-4">
      {[...Array(3)].map((_, idx) => (
        <Card key={idx} className="mb-4">
          <CardHeader className="pb-2">
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/3" />
          </CardHeader>
          <CardContent className="pb-2">
            <div className="flex gap-2 mb-3">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-6 w-16 rounded-full" />
              ))}
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
          <CardFooter className="pt-2 flex justify-between">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-8 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );
  const hamburgerMenu = useBoolean();
  return (
    <>
      <HeaderBase hamburgerMenu={hamburgerMenu} />
      <div className="container mx-auto py-6 max-w-4xl mt-24">
        <h1 className="text-2xl font-bold mb-6">Request Feed</h1>

        <Tabs defaultValue="active" className="mb-6">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="active">Active Requests</TabsTrigger>
              <TabsTrigger value="responded">My Responses</TabsTrigger>
            </TabsList>

            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <Select
                value={filter.offering}
                onValueChange={(value) =>
                  setFilter({ ...filter, offering: value })
                }
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Offering Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Offering Types</SelectItem>
                  <SelectItem value="Dine In">Dine-In</SelectItem>
                  <SelectItem value="Take Away">Take Away</SelectItem>
                  <SelectItem value="Delivery">Delivery</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filter.distance}
                onValueChange={(value) =>
                  setFilter({ ...filter, distance: value })
                }
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Distance" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any Distance</SelectItem>
                  <SelectItem value="5">Up to 5 miles</SelectItem>
                  <SelectItem value="10">Up to 10 miles</SelectItem>
                  <SelectItem value="20">Up to 20 miles</SelectItem>
                  <SelectItem value="50">Up to 50 miles</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <TabsContent value="active" className="mt-6">
            {loading ? (
              renderSkeleton()
            ) : requests.length > 0 ? (
              <div>
                {requests.map(renderRequestItem)}

                {totalPages > 1 && (
                  <div className="flex justify-center mt-6 space-x-2">
                    <Button
                      variant="outline"
                      onClick={() => setPage((p) => Math.max(1, p - 1))}
                      disabled={page === 1}
                    >
                      Previous
                    </Button>
                    <span className="flex items-center px-4">
                      Page {page} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      onClick={() =>
                        setPage((p) => Math.min(totalPages, p + 1))
                      }
                      disabled={page === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <MapPin className="mx-auto h-12 w-12 mb-4 text-gray-400" />
                <h3 className="text-lg font-semibold mb-2 text-foreground">
                  No Active Requests Found
                </h3>
                <p className="mb-1">
                  There are currently no requests matching your filters.
                </p>
                <p>
                  Try adjusting your search criteria or check back soon for new
                  opportunities!
                </p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="responded" className="mt-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium mb-2">Your responses</h3>
              <p className="text-gray-500">
                When you respond to requests, they&apos;ll appear here for easy
                tracking
              </p>
              <Button
                className="mt-4"
                onClick={() => router.push(paths.host.responses)}
              >
                View All Responses
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </>
  );
};

export default HostRequestFeed;
